# =============================================================================
# FAST TRAVEL BACKEND GO - DEVELOPMENT ENVIRONMENT CONFIGURATION
# Based on Python FastAPI backend configuration for seamless migration
# =============================================================================

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=8080
GIN_MODE=debug
DEBUG_MODE=true
ENABLE_PROFILING=false
ENABLE_SWAGGER=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=Vishnu123

# Database Names (matching Python backend)
AUTH_DATABASE_NAME=tdb_auth
FLIGHT_DATABASE_NAME=tdb_flight
BOOKING_DATABASE_NAME=tdb_booking

# Database URLs (for compatibility with Python services)
AUTH_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@localhost/tdb_auth
FLIGHT_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@localhost/tdb_flight
BOOKING_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@localhost/tdb_booking

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_SERVER_HOST=localhost
REDIS_SERVER_PORT=6379
REDIS_SERVER_DB=0

# =============================================================================
# JWT & AUTHENTICATION CONFIGURATION
# =============================================================================
JWT_SECRET_KEY=dev_jwt_secret_key_for_development_only_change_in_production
JWT_EXPIRATION_HOURS=24
JWT_ISSUER=fast-travel-backend-go
ACCESS_TOKEN_EXPIRE_MINUTES=6000
SECRET_KEY=your_jwt_secret_key

# =============================================================================
# TRIPJACK API CONFIGURATION (Test Environment)
# =============================================================================
TRIPJACK_BASE_URL=https://apitest.tripjack.com/
TRIPJACK_API_KEY=6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9
TRIPJACK_USERNAME=your_tripjack_username
TRIPJACK_PASSWORD=your_tripjack_password

# =============================================================================
# CACHE CONFIGURATION (Optimized for Development)
# =============================================================================
MEMORY_CACHE_TTL_MINUTES=15
REDIS_CACHE_TTL_HOURS=1
CACHE_CLEANUP_INTERVAL_MINUTES=5

# Flight Search Cache Timers (in seconds)
FLIGHT_SEARCH_CACHE_TIMER=1800
FLIGHT_SEARCH_POPULAR_CACHE_TIMER=3600
FLIGHT_SEARCH_HOT_CACHE_TIMER=7200
FLIGHT_DETAIL_CACHE_TIMER=600
FLIGHT_DETAIL_PREMIUM_CACHE_TIMER=300
FLIGHT_PRICING_CACHE_TIMER=180
BOOKING_DETAILS_CACHE_TIMER=3600
USER_BOOKINGS_CACHE_TIMER=1800
AIRPORT_DATA_CACHE_TIMER=86400
AIRLINE_DATA_CACHE_TIMER=43200
ROUTE_METADATA_CACHE_TIMER=21600

# Memory Cache Configuration
MEMORY_CACHE_TTL=60
MEMORY_CACHE_HOT_TTL=120
MEMORY_CACHE_MAX_SIZE=10000

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
EXTERNAL_API_TIMEOUT_SECONDS=30
MAX_CONCURRENT_REQUESTS=100
CIRCUIT_BREAKER_THRESHOLD=5
REQUEST_TIMEOUT_SECONDS=25
RETRY_ATTEMPTS=2
RETRY_DELAY_SECONDS=0.5

# Response Time Targets
RESPONSE_TIME_TARGET_MS=3000
RESPONSE_TIME_WARNING_MS=2000
SLOW_QUERY_THRESHOLD_MS=500

# Connection Pool Settings
MAX_IDLE_CONNECTIONS=10
MAX_OPEN_CONNECTIONS=100
CONNECTION_MAX_LIFETIME_HOURS=1
CONNECTION_MAX_IDLE_TIME_MINUTES=30
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_CONNECTION_POOL_MAX_OVERFLOW=30
DATABASE_QUERY_TIMEOUT=30

# =============================================================================
# ENHANCED SEARCH FEATURES
# =============================================================================
ENHANCED_SEARCH_ENABLED=true
FUZZY_SEARCH_ENABLED=true
INTELLIGENT_RANKING_ENABLED=true
PREDICTIVE_CACHING_ENABLED=true
REQUEST_DEDUPLICATION_ENABLED=true
ASYNC_PROCESSING_ENABLED=true

# Deduplication Configuration
DEDUPLICATION_WINDOW_SECONDS=30
MAX_PENDING_REQUESTS=1000
DEDUPLICATION_CLEANUP_INTERVAL=60

# =============================================================================
# MONITORING & METRICS
# =============================================================================
ENABLE_PERFORMANCE_METRICS=true
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_RETENTION_DAYS=7
TRACING_ENABLED=false
CACHE_HIT_RATE_ALERT_THRESHOLD=0.85
CONCURRENT_REQUEST_LIMIT=100

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=json

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL_SECONDS=30

# =============================================================================
# BACKGROUND JOB CONFIGURATION
# =============================================================================
BACKGROUND_JOB_WORKERS=5
BACKGROUND_JOB_QUEUE_SIZE=1000

# =============================================================================
# PAYMENT GATEWAY CONFIGURATION (Development/Test)
# =============================================================================
# Razorpay Test Configuration
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_test_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
RAZORPAY_BASE_URL=https://api.razorpay.com/v1

# PayU Test Configuration
PAYU_MERCHANT_KEY=your_test_merchant_key
PAYU_SALT=your_test_salt
PAYU_BASE_URL=https://test.payu.in

# Paytm Test Configuration
PAYTM_MERCHANT_ID=your_test_merchant_id
PAYTM_MERCHANT_KEY=your_test_merchant_key
PAYTM_WEBSITE=WEBSTAGING
PAYTM_BASE_URL=https://securegw-stage.paytm.in

# =============================================================================
# EMAIL CONFIGURATION (for OTP and notifications)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# =============================================================================
# SMS CONFIGURATION (for OTP)
# =============================================================================
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf
UPLOAD_PATH=./uploads

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
BCRYPT_COST=12
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# =============================================================================
# PROVIDER CONFIGURATION
# =============================================================================
PROVIDER_REQUEST_TIMEOUT=25
PROVIDER_RETRY_ATTEMPTS=3
PROVIDER_CIRCUIT_BREAKER_ENABLED=true

# =============================================================================
# DEVELOPMENT SPECIFIC SETTINGS
# =============================================================================
# Hot reload and development tools
HOT_RELOAD_ENABLED=true
AUTO_MIGRATE_ENABLED=true
SEED_DATA_ENABLED=false

# Development database settings
DEV_DATABASE_RESET_ON_START=false
DEV_ENABLE_QUERY_LOGGING=true
DEV_ENABLE_SLOW_QUERY_LOG=true

# Development cache settings
DEV_CACHE_DISABLED=false
DEV_CACHE_VERBOSE_LOGGING=true

# Development API settings
DEV_MOCK_EXTERNAL_APIS=false
DEV_ENABLE_API_DOCS=true
DEV_ENABLE_CORS_ALL=true
