name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  GO_VERSION: '1.23'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/gobackend

jobs:
  # Code Quality and Testing
  test:
    name: Test and Code Quality
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: |
        go mod download
        go mod verify

    - name: Install tools
      run: |
        go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

    - name: Format check
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Code is not formatted properly:"
          gofmt -s -l .
          exit 1
        fi

    - name: Lint
      run: golangci-lint run --timeout=5m

    - name: Security scan
      run: gosec ./...

    - name: Run unit tests
      env:
        DATABASE_HOST: localhost
        DATABASE_PORT: 3306
        DATABASE_USER: root
        DATABASE_PASSWORD: testpassword
        AUTH_DATABASE_NAME: test_auth
        FLIGHT_DATABASE_NAME: test_flight
        BOOKING_DATABASE_NAME: test_booking
        REDIS_URL: redis://localhost:6379/0
        JWT_SECRET_KEY: test_secret_key_for_ci
      run: |
        go test -v -race -coverprofile=coverage.out ./...

    - name: Run integration tests
      env:
        DATABASE_HOST: localhost
        DATABASE_PORT: 3306
        DATABASE_USER: root
        DATABASE_PASSWORD: testpassword
        AUTH_DATABASE_NAME: test_auth
        FLIGHT_DATABASE_NAME: test_flight
        BOOKING_DATABASE_NAME: test_booking
        REDIS_URL: redis://localhost:6379/0
        JWT_SECRET_KEY: test_secret_key_for_ci
      run: |
        go test -v -tags=integration ./tests/

    - name: Generate coverage report
      run: |
        go tool cover -html=coverage.out -o coverage.html
        go tool cover -func=coverage.out

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella

    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports
        path: |
          coverage.out
          coverage.html

  # Performance Testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run performance tests
      run: |
        go test -v -bench=. -benchmem ./tests/ > performance_results.txt

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance_results.txt

  # Build
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Build application
      run: |
        CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
          -ldflags="-w -s -X main.version=${{ github.sha }}" \
          -o main cmd/server/main.go

    - name: Upload build artifact
      uses: actions/upload-artifact@v3
      with:
        name: gobackend-binary
        path: main

  # Docker Build and Push
  docker:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.event_name == 'push'

    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: docker
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # For example, using kubectl, helm, or docker-compose

    - name: Run health check
      run: |
        echo "Running health check on staging..."
        # Add health check commands here

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker, security]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

    - name: Run health check
      run: |
        echo "Running health check on production..."
        # Add health check commands here

    - name: Notify deployment
      run: |
        echo "Production deployment completed successfully!"
        # Add notification logic here (Slack, email, etc.)

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
    - name: Clean up old artifacts
      run: |
        echo "Cleaning up old artifacts..."
        # Add cleanup commands here
