# Fast Travel Backend Go - Deployment Guide

## Overview

This guide covers the complete deployment process for the Fast Travel Backend Go application, from development to production environments.

## Prerequisites

### System Requirements
- **Go**: 1.21 or higher
- **Docker**: 20.10 or higher
- **Docker Compose**: 2.0 or higher
- **MySQL**: 8.0 or higher
- **Redis**: 6.0 or higher

### Development Tools
- **Make**: For build automation
- **Git**: For version control
- **golangci-lint**: For code linting
- **gosec**: For security scanning

## Environment Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd gobackend
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
vim .env
```

### 3. Install Dependencies
```bash
# Using Make
make deps

# Or manually
go mod download
go mod verify
```

## Development Deployment

### Local Development
```bash
# Quick start
make quick-start

# Or step by step
make deps
make test
make run
```

### Development with Hot Reload
```bash
# Install air for hot reload
go install github.com/cosmtrek/air@latest

# Run with hot reload
make dev
```

### Docker Development
```bash
# Build and run with Docker Compose
make compose-up

# View logs
make compose-logs

# Stop services
make compose-down
```

## Testing

### Unit Tests
```bash
# Run unit tests
make test

# Run with coverage
make test-coverage
```

### Integration Tests
```bash
# Run integration tests
make test-integration
```

### Performance Tests
```bash
# Run performance tests
make test-performance

# Run load tests
make load-test

# Run stress tests
make stress-test
```

### All Tests
```bash
# Run all tests
make test-all
```

## Production Deployment

### 1. Build for Production
```bash
# Build production binary
make build-prod

# Build Docker image
make docker-build
```

### 2. Database Setup

#### MySQL Configuration
```sql
-- Create databases
CREATE DATABASE tdb_auth;
CREATE DATABASE tdb_flight;
CREATE DATABASE tdb_booking;

-- Create user
CREATE USER 'fasttravel'@'%' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON tdb_auth.* TO 'fasttravel'@'%';
GRANT ALL PRIVILEGES ON tdb_flight.* TO 'fasttravel'@'%';
GRANT ALL PRIVILEGES ON tdb_booking.* TO 'fasttravel'@'%';
FLUSH PRIVILEGES;
```

#### Redis Configuration
```bash
# Redis configuration for production
redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
```

### 3. Environment Variables

#### Production .env
```bash
# Server Configuration
PORT=8080
GIN_MODE=release

# Database Configuration
DATABASE_HOST=mysql-server
DATABASE_PORT=3306
DATABASE_USER=fasttravel
DATABASE_PASSWORD=secure_password

# Database Names
AUTH_DATABASE_NAME=tdb_auth
FLIGHT_DATABASE_NAME=tdb_flight
BOOKING_DATABASE_NAME=tdb_booking

# Redis Configuration
REDIS_URL=redis://redis-server:6379/0

# JWT Configuration
JWT_SECRET_KEY=your_production_jwt_secret_key_here
JWT_EXPIRATION_HOURS=24

# TripJack API Configuration
TRIPJACK_BASE_URL=https://api.tripjack.com/
TRIPJACK_API_KEY=your_production_api_key
TRIPJACK_USERNAME=your_production_username
TRIPJACK_PASSWORD=your_production_password

# Payment Gateway Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
PAYU_MERCHANT_KEY=your_payu_merchant_key
PAYU_SALT=your_payu_salt
```

### 4. Docker Deployment

#### Single Container
```bash
# Build image
docker build -t fast-travel-backend-go:latest .

# Run container
docker run -d \
  --name fast-travel-backend \
  -p 8080:8080 \
  --env-file .env \
  fast-travel-backend-go:latest
```

#### Docker Compose Production
```bash
# Deploy with production compose
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### 5. Kubernetes Deployment

#### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: fast-travel
```

#### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: fast-travel
data:
  PORT: "8080"
  GIN_MODE: "release"
  DATABASE_HOST: "mysql-service"
  DATABASE_PORT: "3306"
  REDIS_URL: "redis://redis-service:6379/0"
```

#### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: fast-travel
type: Opaque
stringData:
  DATABASE_PASSWORD: "secure_password"
  JWT_SECRET_KEY: "your_jwt_secret_key"
  TRIPJACK_API_KEY: "your_api_key"
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fast-travel-backend
  namespace: fast-travel
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fast-travel-backend
  template:
    metadata:
      labels:
        app: fast-travel-backend
    spec:
      containers:
      - name: backend
        image: fast-travel-backend-go:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: fast-travel-backend-service
  namespace: fast-travel
spec:
  selector:
    app: fast-travel-backend
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## Monitoring and Observability

### Health Checks
```bash
# Application health
curl http://localhost:8080/health

# Database health
curl http://localhost:8080/health | jq '.database'
```

### Metrics Collection
```bash
# Start monitoring stack
make monitor-up

# Access Prometheus
open http://localhost:9090

# Access Grafana
open http://localhost:3000
```

### Log Management
```bash
# View application logs
docker-compose logs -f gobackend

# View specific service logs
docker-compose logs -f mysql
docker-compose logs -f redis
```

## Performance Optimization

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_bookings_user_id ON master_bookings(user_id);
CREATE INDEX idx_bookings_reference ON master_bookings(booking_reference);
CREATE INDEX idx_payments_booking_ref ON payments(booking_reference);
```

### Cache Configuration
```bash
# Redis memory optimization
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET maxmemory 2gb
```

### Application Tuning
```bash
# Environment variables for performance
export GOMAXPROCS=4
export GOGC=100
export GOMEMLIMIT=2GiB
```

## Security Considerations

### SSL/TLS Configuration
```nginx
# Nginx SSL configuration
server {
    listen 443 ssl http2;
    server_name api.fasttravel.com;
    
    ssl_certificate /etc/ssl/certs/fasttravel.crt;
    ssl_certificate_key /etc/ssl/private/fasttravel.key;
    
    location / {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Firewall Rules
```bash
# Allow only necessary ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 8080/tcp   # Block direct access to app
```

## Backup and Recovery

### Database Backup
```bash
# MySQL backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -h mysql-server -u fasttravel -p \
  --single-transaction \
  --routines \
  --triggers \
  --all-databases > backup_$DATE.sql
```

### Redis Backup
```bash
# Redis backup
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb backup_redis_$(date +%Y%m%d).rdb
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
mysql -h DATABASE_HOST -u DATABASE_USER -p

# Check database status
docker-compose exec mysql mysqladmin status
```

#### Redis Connection Issues
```bash
# Check Redis connectivity
redis-cli -h REDIS_HOST ping

# Check Redis status
docker-compose exec redis redis-cli info
```

#### Application Issues
```bash
# Check application logs
docker-compose logs gobackend

# Check resource usage
docker stats

# Check health endpoint
curl -f http://localhost:8080/health
```

### Performance Issues
```bash
# Check CPU and memory usage
top -p $(pgrep main)

# Check database performance
mysql -e "SHOW PROCESSLIST;"

# Check Redis performance
redis-cli info stats
```

## Rollback Procedures

### Application Rollback
```bash
# Rollback to previous version
docker-compose down
docker tag fast-travel-backend-go:previous fast-travel-backend-go:latest
docker-compose up -d
```

### Database Rollback
```bash
# Restore from backup
mysql -h mysql-server -u fasttravel -p < backup_YYYYMMDD_HHMMSS.sql
```

## Maintenance

### Regular Maintenance Tasks
```bash
# Update dependencies
make deps
make tidy

# Security updates
make security-scan

# Performance monitoring
make test-performance

# Log rotation
logrotate /etc/logrotate.d/fasttravel
```

### Scaling
```bash
# Scale horizontally
docker-compose up -d --scale gobackend=3

# Scale with Kubernetes
kubectl scale deployment fast-travel-backend --replicas=5
```

## Support and Documentation

### API Documentation
- Swagger UI: `http://localhost:8080/swagger/`
- OpenAPI Spec: `./api/openapi.yaml`

### Monitoring Dashboards
- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3000`

### Log Aggregation
- Application logs: `/var/log/fasttravel/`
- Access logs: `/var/log/nginx/`
- Error logs: `/var/log/nginx/error.log`
