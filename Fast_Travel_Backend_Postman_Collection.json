{
	"info": {
		"_postman_id": "fast-travel-backend-go",
		"name": "Fast Travel Backend - Go API Collection",
		"description": "Complete API collection for the Go backend flight booking system with authentication, flight search, booking, and payment endpoints.",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
		"_exporter_id": "fast-travel-backend"
	},
	"item": [
		{
			"name": "Health Check",
			"request": {
				"method": "GET",
				"header": [],
				"url": {
					"raw": "{{base_url}}/health",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"health"
					]
				}
			},
			"response": []
		},
		{
			"name": "Authentication",
			"item": [
				{
					"name": "Register User",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"phone_number\": \"**********\",\n  \"phone_country_code\": \"+91\",\n  \"role\": \"CUSTOMER\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/auth/register",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"register"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Login OTP",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/apis/auth/get_login_otp/1",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"get_login_otp",
								"1"
							]
						}
					},
					"response": []
				},
				{
					"name": "Verify OTP",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/auth/otp_verify",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"otp_verify"
							]
						}
					},
					"response": []
				},
				{
					"name": "Login with OTP",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/auth/login?email=<EMAIL>&otp=123456",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"login"
							],
							"query": [
								{
									"key": "email",
									"value": "<EMAIL>"
								},
								{
									"key": "otp",
									"value": "123456"
								}
							]
						}
					},
					"response": []
				},
				{
					"name": "Resend OTP",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/auth/resend-otp",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"resend-otp"
							]
						}
					},
					"response": []
				},
				{
					"name": "Refresh Token",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							},
							{
								"key": "Authorization",
								"value": "Bearer {{auth_token}}"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/auth/refresh",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"refresh"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get User Profile",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{auth_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/apis/auth/profile",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"auth",
								"profile"
							]
						}
					},
					"response": []
				}
			]
		},
		{
			"name": "Flight Search & Info",
			"item": [
				{
					"name": "Search Airports",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"query\": \"Delhi\",\n  \"limit\": 10\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/airports",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"airports"
							]
						}
					},
					"response": []
				},
				{
					"name": "Search Flights",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"SecType\": \"DOM\",\n  \"FareType\": \"REGULAR\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"Source\": \"WEB\",\n  \"Mode\": \"SS\",\n  \"ClientID\": \"test_client\",\n  \"IsMultipleCarrier\": false,\n  \"IsRefundable\": false,\n  \"TUI\": \"{{$randomUUID}}\",\n  \"YTH\": 0,\n  \"Trips\": [\n    {\n      \"Origin\": \"DEL\",\n      \"Destination\": \"BOM\",\n      \"DepartureDate\": \"2024-12-25\"\n    }\n  ]\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/search",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"search"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Cached Search Results",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/search_list",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"search_list"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Flight Details",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"FareId\": \"fare_id_from_search\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/details",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"details"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Flight Pricing",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"Trips\": [\n    {\n      \"Amount\": 5000.0,\n      \"Index\": \"0\",\n      \"OrderID\": 1,\n      \"TUI\": \"search_tui_from_previous_search\"\n    }\n  ],\n  \"ClientID\": \"test_client\",\n  \"Mode\": \"SS\",\n  \"Options\": \"A\",\n  \"Source\": \"SF\",\n  \"TripType\": \"O\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/pricing",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"pricing"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Fare Rules",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"FareId\": \"fare_id_from_search\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/rules",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"rules"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Flight Services (SSR)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"FareId\": \"fare_id_from_search\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/services",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"services"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Web Settings",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"ClientID\": \"test_client\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/setup",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"setup"
							]
						}
					},
					"response": []
				},
				{
					"name": "Get Flight Seat Map",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"FareId\": \"fare_id_from_search\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/apis/seat",
							"host": [
								"{{base_url}}"
							],
							"path": [
								"apis",
								"seat"
							]
						}
					},
					"response": []
				}
			]
		}
	},
	{
		"name": "Booking Management",
		"item": [
			{
				"name": "Create Booking",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{auth_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"flight_booking\": {\n    \"provider_info\": {\n      \"code\": \"TJ\"\n    },\n    \"TUI\": \"search_tui_from_previous_search\",\n    \"ADT\": 1,\n    \"CHD\": 0,\n    \"INF\": 0,\n    \"NetAmount\": 5000.0,\n    \"AirlineNetFare\": 4500.0,\n    \"GrossAmount\": 5500.0,\n    \"Trips\": [\n      {\n        \"provider_info\": {\n          \"code\": \"TJ\"\n        },\n        \"Journeys\": [\n          {\n            \"Provider\": \"TJ\",\n            \"Stops\": 0,\n            \"GrossFare\": 5500.0,\n            \"NetFare\": 5000.0,\n            \"Segments\": [\n              {\n                \"Flight\": {\n                  \"FUID\": \"flight_unique_id\",\n                  \"VAC\": \"6E\",\n                  \"MAC\": \"6E\",\n                  \"OAC\": \"6E\",\n                  \"Airline\": \"IndiGo\",\n                  \"FlightNo\": \"6E123\",\n                  \"ArrivalTime\": \"2024-12-25T14:30:00\",\n                  \"DepartureTime\": \"2024-12-25T12:00:00\",\n                  \"ArrivalCode\": \"BOM\",\n                  \"DepartureCode\": \"DEL\"\n                },\n                \"Fare\": {\n                  \"PTCFare\": [\n                    {\n                      \"PTC\": \"ADT\",\n                      \"Fare\": 4500.0,\n                      \"Tax\": 1000.0,\n                      \"GrossFare\": 5500.0,\n                      \"NetFare\": 5000.0\n                    }\n                  ],\n                  \"GrossFare\": 5500.0,\n                  \"NetFare\": 5000.0\n                }\n              }\n            ]\n          }\n        ]\n      }\n    ],\n    \"Hold\": false\n  },\n  \"Travellers\": [\n    {\n      \"Title\": \"Mr\",\n      \"FName\": \"John\",\n      \"LName\": \"Doe\",\n      \"DOB\": \"1990-01-15\",\n      \"Gender\": \"M\",\n      \"PTC\": \"ADT\",\n      \"PassportNo\": \"A12345678\",\n      \"PassportExpiry\": \"2030-01-15\",\n      \"Nationality\": \"IN\"\n    }\n  ],\n  \"ContactInfo\": {\n    \"email\": \"<EMAIL>\",\n    \"phn_country_code\": \"+91\",\n    \"phone\": \"**********\"\n  }\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/create-booking",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"create-booking"
						]
					}
				},
				"response": []
			},
			{
				"name": "Get User Bookings",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{auth_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/user-bookings?page=1&limit=10",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"user-bookings"
						],
						"query": [
							{
								"key": "page",
								"value": "1"
							},
							{
								"key": "limit",
								"value": "10"
							}
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Booking by Reference",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{auth_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/get-booking/BOOK123456",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"get-booking",
							"BOOK123456"
						]
					}
				},
				"response": []
			},
			{
				"name": "Cancel Booking",
				"request": {
					"method": "PUT",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{auth_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"reason\": \"Change of plans\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/booking/BOOK123456/cancel",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"booking",
							"BOOK123456",
							"cancel"
						]
					}
				},
				"response": []
			}
		]
	},
	{
		"name": "Payment Management",
		"item": [
			{
				"name": "Initiate Payment",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{auth_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"booking_reference\": \"BOOK123456\",\n  \"amount\": 5500.0,\n  \"currency\": \"INR\",\n  \"method\": \"card\",\n  \"gateway\": \"razorpay\",\n  \"callback_url\": \"https://yourapp.com/payment/success\",\n  \"cancel_url\": \"https://yourapp.com/payment/cancel\",\n  \"customer_info\": {\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91**********\"\n  }\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/initiate",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"initiate"
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Payment Status",
				"request": {
					"method": "GET",
					"header": [],
					"url": {
						"raw": "{{base_url}}/apis/payment/PAY123456/status",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"PAY123456",
							"status"
						]
					}
				},
				"response": []
			},
			{
				"name": "Razorpay Callback",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"razorpay_payment_id\": \"pay_123456789\",\n  \"razorpay_order_id\": \"order_123456789\",\n  \"razorpay_signature\": \"signature_hash\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/callback/razorpay",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"callback",
							"razorpay"
						]
					}
				},
				"response": []
			},
			{
				"name": "PayU Callback",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/x-www-form-urlencoded"
						}
					],
					"body": {
						"mode": "urlencoded",
						"urlencoded": [
							{
								"key": "mihpayid",
								"value": "403993715521273379",
								"type": "text"
							},
							{
								"key": "mode",
								"value": "CC",
								"type": "text"
							},
							{
								"key": "status",
								"value": "success",
								"type": "text"
							},
							{
								"key": "unmappedstatus",
								"value": "captured",
								"type": "text"
							},
							{
								"key": "key",
								"value": "merchant_key",
								"type": "text"
							},
							{
								"key": "txnid",
								"value": "PAY123456",
								"type": "text"
							},
							{
								"key": "amount",
								"value": "5500.00",
								"type": "text"
							},
							{
								"key": "productinfo",
								"value": "Flight Booking",
								"type": "text"
							},
							{
								"key": "firstname",
								"value": "John",
								"type": "text"
							},
							{
								"key": "email",
								"value": "<EMAIL>",
								"type": "text"
							},
							{
								"key": "hash",
								"value": "payment_hash",
								"type": "text"
							}
						]
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/callback/payu",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"callback",
							"payu"
						]
					}
				},
				"response": []
			},
			{
				"name": "Paytm Callback",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/x-www-form-urlencoded"
						}
					],
					"body": {
						"mode": "urlencoded",
						"urlencoded": [
							{
								"key": "ORDERID",
								"value": "PAY123456",
								"type": "text"
							},
							{
								"key": "MID",
								"value": "merchant_id",
								"type": "text"
							},
							{
								"key": "TXNID",
								"value": "20210817111212800110168688203008548",
								"type": "text"
							},
							{
								"key": "TXNAMOUNT",
								"value": "5500.00",
								"type": "text"
							},
							{
								"key": "PAYMENTMODE",
								"value": "CC",
								"type": "text"
							},
							{
								"key": "CURRENCY",
								"value": "INR",
								"type": "text"
							},
							{
								"key": "TXNDATE",
								"value": "2021-08-17 11:12:12.0",
								"type": "text"
							},
							{
								"key": "STATUS",
								"value": "TXN_SUCCESS",
								"type": "text"
							},
							{
								"key": "RESPCODE",
								"value": "01",
								"type": "text"
							},
							{
								"key": "RESPMSG",
								"value": "Txn Success",
								"type": "text"
							},
							{
								"key": "CHECKSUMHASH",
								"value": "checksum_hash",
								"type": "text"
							}
						]
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/callback/paytm",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"callback",
							"paytm"
						]
					}
				},
				"response": []
			},
			{
				"name": "Payment Webhook",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"event\": \"payment.captured\",\n  \"payment_id\": \"pay_123456789\",\n  \"order_id\": \"order_123456789\",\n  \"amount\": 550000,\n  \"currency\": \"INR\",\n  \"status\": \"captured\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/webhook/razorpay",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"webhook",
							"razorpay"
						]
					}
				},
				"response": []
			}
		]
	},
	{
		"name": "Admin Endpoints",
		"item": [
			{
				"name": "Update Booking Status",
				"request": {
					"method": "PUT",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"status\": \"confirmed\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/booking/admin/BOOK123456/status",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"booking",
							"admin",
							"BOOK123456",
							"status"
						]
					}
				},
				"response": []
			},
			{
				"name": "Update Payment Status",
				"request": {
					"method": "PUT",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"payment_status\": \"paid\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/booking/admin/BOOK123456/payment-status",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"booking",
							"admin",
							"BOOK123456",
							"payment-status"
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Booking Statistics",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/booking/admin/stats?period=month",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"booking",
							"admin",
							"stats"
						],
						"query": [
							{
								"key": "period",
								"value": "month"
							}
						]
					}
				},
				"response": []
			},
			{
				"name": "Process Refund",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						},
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"payment_id\": \"PAY123456\",\n  \"amount\": 5500.0,\n  \"reason\": \"Customer requested cancellation\",\n  \"refund_type\": \"full\",\n  \"notes\": \"Processed due to flight cancellation\"\n}"
					},
					"url": {
						"raw": "{{base_url}}/apis/payment/admin/refund",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"admin",
							"refund"
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Payment Analytics",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/payment/admin/analytics?period=month",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"admin",
							"analytics"
						],
						"query": [
							{
								"key": "period",
								"value": "month"
							}
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Payment Method Statistics",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/payment/admin/method-stats",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"admin",
							"method-stats"
						]
					}
				},
				"response": []
			},
			{
				"name": "Get Gateway Statistics",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/payment/admin/gateway-stats",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"payment",
							"admin",
							"gateway-stats"
						]
					}
				},
				"response": []
			},
			{
				"name": "Dashboard - Get All Bookings",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/dashboard/all-bookings?page=1&limit=20",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"dashboard",
							"all-bookings"
						],
						"query": [
							{
								"key": "page",
								"value": "1"
							},
							{
								"key": "limit",
								"value": "20"
							}
						]
					}
				},
				"response": []
			},
			{
				"name": "Dashboard - Get Booking by ID",
				"request": {
					"method": "GET",
					"header": [
						{
							"key": "Authorization",
							"value": "Bearer {{admin_token}}"
						}
					],
					"url": {
						"raw": "{{base_url}}/apis/dashboard/bookings/BOOK123456",
						"host": [
							"{{base_url}}"
						],
						"path": [
							"apis",
							"dashboard",
							"bookings",
							"BOOK123456"
						]
					}
				},
				"response": []
			}
		]
	}
],
"event": [
	{
		"listen": "prerequest",
		"script": {
			"type": "text/javascript",
			"exec": [
				""
			]
		}
	},
	{
		"listen": "test",
		"script": {
			"type": "text/javascript",
			"exec": [
				""
			]
		}
	}
],
"variable": [
	{
		"key": "base_url",
		"value": "http://localhost:8080",
		"type": "string"
	},
	{
		"key": "auth_token",
		"value": "",
		"type": "string"
	},
	{
		"key": "admin_token",
		"value": "",
		"type": "string"
	},
	{
		"key": "refresh_token",
		"value": "",
		"type": "string"
	},
	{
		"key": "user_id",
		"value": "",
		"type": "string"
	},
	{
		"key": "booking_reference",
		"value": "",
		"type": "string"
	},
	{
		"key": "payment_id",
		"value": "",
		"type": "string"
	},
	{
		"key": "search_tui",
		"value": "",
		"type": "string"
	}
]
}