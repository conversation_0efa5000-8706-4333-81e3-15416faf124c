# Fast Travel Backend Go - Makefile

# Variables
APP_NAME=gobackend
BINARY_NAME=main
DOCKER_IMAGE=fast-travel-backend-go
VERSION?=latest
PORT?=8080

# Go related variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt

# Build flags
BUILD_FLAGS=-ldflags="-w -s"
TEST_FLAGS=-v -race -coverprofile=coverage.out

.PHONY: help build clean test test-coverage test-integration test-performance run dev docker-build docker-run docker-push lint format deps tidy security-scan

# Default target
all: clean deps test build

# Help target
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build the application
build: ## Build the application binary
	@echo "Building $(APP_NAME)..."
	$(GOBUILD) $(BUILD_FLAGS) -o $(BINARY_NAME) cmd/server/main.go
	@echo "Build completed: $(BINARY_NAME)"

# Build for production
build-prod: ## Build the application for production
	@echo "Building $(APP_NAME) for production..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BINARY_NAME) cmd/server/main.go
	@echo "Production build completed: $(BINARY_NAME)"

# Clean build artifacts
clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f coverage.out
	rm -f coverage.html
	@echo "Clean completed"

# Run tests
test: ## Run unit tests
	@echo "Running tests..."
	$(GOTEST) $(TEST_FLAGS) ./...
	@echo "Tests completed"

# Run tests with coverage
test-coverage: ## Run tests with coverage report
	@echo "Running tests with coverage..."
	$(GOTEST) $(TEST_FLAGS) ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run integration tests
test-integration: ## Run integration tests
	@echo "Running integration tests..."
	$(GOTEST) -v -tags=integration ./tests/
	@echo "Integration tests completed"

# Run performance tests
test-performance: ## Run performance tests and benchmarks
	@echo "Running performance tests..."
	$(GOTEST) -v -bench=. -benchmem ./tests/
	@echo "Performance tests completed"

# Run all tests
test-all: test test-integration test-performance ## Run all tests (unit, integration, performance)

# Run the application
run: ## Run the application
	@echo "Starting $(APP_NAME)..."
	$(GOCMD) run cmd/server/main.go

# Run in development mode with hot reload
dev: ## Run in development mode (requires air)
	@echo "Starting $(APP_NAME) in development mode..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air not found. Install with: go install github.com/cosmtrek/air@latest"; \
		echo "Falling back to regular run..."; \
		$(MAKE) run; \
	fi

# Install dependencies
deps: ## Install dependencies
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) verify
	@echo "Dependencies installed"

# Tidy dependencies
tidy: ## Tidy dependencies
	@echo "Tidying dependencies..."
	$(GOMOD) tidy
	@echo "Dependencies tidied"

# Format code
format: ## Format Go code
	@echo "Formatting code..."
	$(GOFMT) -s -w .
	@echo "Code formatted"

# Lint code
lint: ## Lint Go code (requires golangci-lint)
	@echo "Linting code..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found. Install with:"; \
		echo "curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b \$$(go env GOPATH)/bin v1.54.2"; \
	fi

# Security scan
security-scan: ## Run security scan (requires gosec)
	@echo "Running security scan..."
	@if command -v gosec > /dev/null; then \
		gosec ./...; \
	else \
		echo "gosec not found. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Docker targets
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE):$(VERSION) .
	@echo "Docker image built: $(DOCKER_IMAGE):$(VERSION)"

docker-run: ## Run Docker container
	@echo "Running Docker container..."
	docker run -p $(PORT):8080 --env-file .env $(DOCKER_IMAGE):$(VERSION)

docker-run-detached: ## Run Docker container in detached mode
	@echo "Running Docker container in detached mode..."
	docker run -d -p $(PORT):8080 --env-file .env --name $(APP_NAME) $(DOCKER_IMAGE):$(VERSION)

docker-stop: ## Stop Docker container
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME)
	docker rm $(APP_NAME)

docker-push: ## Push Docker image to registry
	@echo "Pushing Docker image..."
	docker push $(DOCKER_IMAGE):$(VERSION)

# Docker Compose targets
compose-up: ## Start services with Docker Compose
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

compose-down: ## Stop services with Docker Compose
	@echo "Stopping services with Docker Compose..."
	docker-compose down

compose-logs: ## View Docker Compose logs
	@echo "Viewing Docker Compose logs..."
	docker-compose logs -f

compose-build: ## Build services with Docker Compose
	@echo "Building services with Docker Compose..."
	docker-compose build

# Development Docker Compose targets
dev-up: ## Start development environment
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d

dev-down: ## Stop development environment
	@echo "Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

dev-logs: ## View development logs
	@echo "Viewing development logs..."
	docker-compose -f docker-compose.dev.yml logs -f gobackend-dev

dev-build: ## Build development environment
	@echo "Building development environment..."
	docker-compose -f docker-compose.dev.yml build

dev-restart: ## Restart development environment
	@echo "Restarting development environment..."
	$(MAKE) dev-down
	$(MAKE) dev-up

dev-shell: ## Access development container shell
	@echo "Accessing development container shell..."
	docker-compose -f docker-compose.dev.yml exec gobackend-dev sh

dev-tools-up: ## Start development tools (phpMyAdmin, Redis Commander, etc.)
	@echo "Starting development tools..."
	docker-compose -f docker-compose.dev.yml --profile tools up -d

dev-tools-down: ## Stop development tools
	@echo "Stopping development tools..."
	docker-compose -f docker-compose.dev.yml --profile tools down

dev-monitoring-up: ## Start development monitoring stack
	@echo "Starting development monitoring..."
	docker-compose -f docker-compose.dev.yml --profile monitoring up -d

dev-monitoring-down: ## Stop development monitoring stack
	@echo "Stopping development monitoring..."
	docker-compose -f docker-compose.dev.yml --profile monitoring down

# Database targets
db-migrate: ## Run database migrations
	@echo "Running database migrations..."
	$(GOCMD) run cmd/server/main.go --migrate-only

db-seed: ## Seed database with test data
	@echo "Seeding database..."
	$(GOCMD) run scripts/seed.go

# Monitoring targets
monitor-up: ## Start monitoring stack
	@echo "Starting monitoring stack..."
	docker-compose --profile monitoring up -d

monitor-down: ## Stop monitoring stack
	@echo "Stopping monitoring stack..."
	docker-compose --profile monitoring down

# Production targets
deploy-staging: ## Deploy to staging environment
	@echo "Deploying to staging..."
	docker-compose -f docker-compose.staging.yml up -d

deploy-prod: ## Deploy to production environment
	@echo "Deploying to production..."
	docker-compose -f docker-compose.prod.yml up -d

# Health check
health-check: ## Check application health
	@echo "Checking application health..."
	@curl -f http://localhost:$(PORT)/health || echo "Health check failed"

# Load testing
load-test: ## Run load tests (requires hey)
	@echo "Running load tests..."
	@if command -v hey > /dev/null; then \
		hey -n 1000 -c 10 http://localhost:$(PORT)/health; \
	else \
		echo "hey not found. Install with: go install github.com/rakyll/hey@latest"; \
	fi

# Stress testing
stress-test: ## Run stress tests
	@echo "Running stress tests..."
	@if command -v hey > /dev/null; then \
		hey -n 10000 -c 100 -t 30 http://localhost:$(PORT)/health; \
	else \
		echo "hey not found. Install with: go install github.com/rakyll/hey@latest"; \
	fi

# Generate API documentation
docs: ## Generate API documentation
	@echo "Generating API documentation..."
	@if command -v swag > /dev/null; then \
		swag init -g cmd/server/main.go; \
	else \
		echo "swag not found. Install with: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# Setup development environment
setup-dev: ## Setup development environment
	@echo "Setting up development environment..."
	$(MAKE) deps
	@echo "Installing development tools..."
	go install github.com/air-verse/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	go install github.com/swaggo/swag/cmd/swag@latest
	go install github.com/rakyll/hey@latest
	@echo "Development environment setup completed"

# CI/CD targets
ci-test: ## Run CI tests
	@echo "Running CI tests..."
	$(MAKE) lint
	$(MAKE) security-scan
	$(MAKE) test-coverage
	$(MAKE) test-integration

ci-build: ## Build for CI
	@echo "Building for CI..."
	$(MAKE) build-prod

# Cleanup targets
clean-docker: ## Clean Docker images and containers
	@echo "Cleaning Docker..."
	docker system prune -f
	docker image prune -f

clean-all: clean clean-docker ## Clean everything

# Version and release
version: ## Show version information
	@echo "Application: $(APP_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Go version: $$(go version)"
	@echo "Git commit: $$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
	@echo "Build time: $$(date)"

# Quick start
quick-start: ## Quick start for development
	@echo "Quick starting $(APP_NAME)..."
	$(MAKE) deps
	$(MAKE) test
	$(MAKE) run

# Development environment setup
dev-setup: ## Setup complete development environment
	@echo "Setting up development environment..."
	$(MAKE) setup-dev
	cp .env.development .env
	$(MAKE) dev-build
	$(MAKE) dev-up
	@echo "Development environment ready!"
	@echo "Services available at:"
	@echo "  - Go Backend: http://localhost:8080"
	@echo "  - phpMyAdmin: http://localhost:8082 (run 'make dev-tools-up')"
	@echo "  - Redis Commander: http://localhost:8081 (run 'make dev-tools-up')"
	@echo "  - Prometheus: http://localhost:9090 (run 'make dev-monitoring-up')"
	@echo "  - Grafana: http://localhost:3000 (run 'make dev-monitoring-up')"

# Development utilities
dev-reset: ## Reset development environment (clean slate)
	@echo "Resetting development environment..."
	$(MAKE) dev-down
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f
	$(MAKE) dev-up

dev-status: ## Show development environment status
	@echo "Development environment status:"
	docker-compose -f docker-compose.dev.yml ps

dev-db-reset: ## Reset development database with fresh seed data
	@echo "Resetting development database..."
	docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pVishnu123 -e "DROP DATABASE IF EXISTS tdb_auth; DROP DATABASE IF EXISTS tdb_flight; DROP DATABASE IF EXISTS tdb_booking;"
	docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pVishnu123 < /docker-entrypoint-initdb.d/01-init.sql
	docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pVishnu123 < /docker-entrypoint-initdb.d/02-seed.sql
	@echo "Development database reset complete"

dev-cache-clear: ## Clear development Redis cache
	@echo "Clearing development cache..."
	docker-compose -f docker-compose.dev.yml exec redis-dev redis-cli FLUSHALL
	@echo "Development cache cleared"
