# Fast Travel Backend - Postman Collection Guide

## Overview
This comprehensive Postman collection contains all API endpoints for the Fast Travel Backend Go application, including authentication, flight search, booking management, payment processing, and admin functionalities.

## Collection Structure

### 1. Health Check
- **GET** `/health` - Check API server status and database connectivity

### 2. Authentication
- **POST** `/apis/auth/register` - Register new user
- **GET** `/apis/auth/get_login_otp/{userId}` - Get login OTP for user
- **POST** `/apis/auth/otp_verify` - Verify OTP
- **POST** `/apis/auth/login` - Login with O<PERSON> (query params)
- **POST** `/apis/auth/resend-otp` - Resend OTP
- **POST** `/apis/auth/refresh` - Refresh JWT token
- **GET** `/apis/auth/profile` - Get user profile (requires auth)

### 3. Flight Search & Info
- **POST** `/apis/airports` - Search airports
- **POST** `/apis/search` - Search flights
- **POST** `/apis/search_list` - Get cached search results
- **POST** `/apis/details` - Get flight details
- **POST** `/apis/pricing` - Get flight pricing
- **POST** `/apis/rules` - Get fare rules
- **POST** `/apis/services` - Get flight services (SSR)
- **POST** `/apis/setup` - Get web settings
- **POST** `/apis/seat` - Get flight seat map

### 4. Booking Management
- **POST** `/apis/create-booking` - Create new booking (requires auth)
- **GET** `/apis/user-bookings` - Get user bookings (requires auth)
- **GET** `/apis/get-booking/{reference}` - Get booking by reference (requires auth)
- **PUT** `/apis/booking/{reference}/cancel` - Cancel booking (requires auth)

### 5. Payment Management
- **POST** `/apis/payment/initiate` - Initiate payment (requires auth)
- **GET** `/apis/payment/{payment_id}/status` - Get payment status
- **POST** `/apis/payment/callback/razorpay` - Razorpay callback
- **POST** `/apis/payment/callback/payu` - PayU callback (form data)
- **POST** `/apis/payment/callback/paytm` - Paytm callback (form data)
- **POST** `/apis/payment/webhook/{gateway}` - Payment webhooks

### 6. Admin Endpoints (Requires Admin Token)
- **PUT** `/apis/booking/admin/{reference}/status` - Update booking status
- **PUT** `/apis/booking/admin/{reference}/payment-status` - Update payment status
- **GET** `/apis/booking/admin/stats` - Get booking statistics
- **POST** `/apis/payment/admin/refund` - Process refund
- **GET** `/apis/payment/admin/analytics` - Get payment analytics
- **GET** `/apis/payment/admin/method-stats` - Get payment method stats
- **GET** `/apis/payment/admin/gateway-stats` - Get gateway statistics
- **GET** `/apis/dashboard/all-bookings` - Get all bookings (admin)
- **GET** `/apis/dashboard/bookings/{id}` - Get booking by ID (admin)

## Environment Variables

The collection uses the following variables:

| Variable | Description | Example Value |
|----------|-------------|---------------|
| `base_url` | API base URL | `http://localhost:8080` |
| `auth_token` | User JWT token | Auto-populated from login |
| `admin_token` | Admin JWT token | Auto-populated from admin login |
| `refresh_token` | Refresh token | Auto-populated from login |
| `user_id` | Current user ID | Auto-populated |
| `booking_reference` | Booking reference | Auto-populated from booking |
| `payment_id` | Payment ID | Auto-populated from payment |
| `search_tui` | Search transaction ID | Auto-populated from search |

## Getting Started

### 1. Import Collection
1. Open Postman
2. Click "Import"
3. Select `Fast_Travel_Backend_Postman_Collection.json`
4. The collection will be imported with all endpoints

### 2. Set Environment
1. Create a new environment or use the collection variables
2. Set `base_url` to your server URL (default: `http://localhost:8080`)

### 3. Authentication Flow
1. **Register User**: Use "Register User" endpoint
2. **Get OTP**: Use "Get Login OTP" with user ID
3. **Verify OTP**: Use "Verify OTP" endpoint
4. **Login**: Use "Login with OTP" - token will be auto-saved

### 4. Flight Search Flow
1. **Search Airports**: Find airport codes
2. **Search Flights**: Search for available flights
3. **Get Pricing**: Get detailed pricing for selected flights
4. **Get Details**: Get comprehensive flight details

### 5. Booking Flow
1. **Create Booking**: Create booking with traveller details
2. **Initiate Payment**: Start payment process
3. **Handle Callbacks**: Process payment gateway responses

## Sample Request Bodies

### User Registration
```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "phone_number": "9876543210",
  "phone_country_code": "+91",
  "role": "CUSTOMER"
}
```

### Flight Search
```json
{
  "SecType": "DOM",
  "FareType": "REGULAR",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "Source": "WEB",
  "Mode": "SS",
  "ClientID": "test_client",
  "IsMultipleCarrier": false,
  "IsRefundable": false,
  "TUI": "{{$randomUUID}}",
  "YTH": 0,
  "Trips": [
    {
      "Origin": "DEL",
      "Destination": "BOM",
      "DepartureDate": "2024-12-25"
    }
  ]
}
```

### Payment Initiation
```json
{
  "booking_reference": "BOOK123456",
  "amount": 5500.0,
  "currency": "INR",
  "method": "card",
  "gateway": "razorpay",
  "callback_url": "https://yourapp.com/payment/success",
  "cancel_url": "https://yourapp.com/payment/cancel",
  "customer_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+************"
  }
}
```

## Authentication Requirements

### User Endpoints
- Require `Authorization: Bearer {{auth_token}}` header
- Token obtained from login/OTP verification

### Admin Endpoints
- Require `Authorization: Bearer {{admin_token}}` header
- Admin role required in JWT token

### Public Endpoints
- Health check
- Payment callbacks/webhooks
- Payment status (by payment ID)

## Error Handling

All endpoints return standardized error responses:

```json
{
  "error": "Error type",
  "message": "Detailed error message",
  "code": 400
}
```

## Success Responses

All successful endpoints return:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": { /* response data */ }
}
```

## Testing Tips

1. **Start with Health Check** to verify server connectivity
2. **Use Variables** to store tokens and IDs automatically
3. **Follow the Flow** - Register → Login → Search → Book → Pay
4. **Check Logs** in your Go application for debugging
5. **Use Test Scripts** in Postman to auto-extract tokens and IDs

## Payment Gateway Testing

### Razorpay
- Use test credentials in environment variables
- Test with sample card numbers from Razorpay docs

### PayU
- Use test merchant credentials
- Test with PayU sandbox environment

### Paytm
- Use staging environment credentials
- Test with Paytm test wallet

## Support

For issues or questions:
1. Check the Go application logs
2. Verify environment variables are set correctly
3. Ensure database is running and accessible
4. Check network connectivity to external APIs (TripJack)

## Version
Collection Version: 1.0.0
Compatible with: Fast Travel Backend Go v1.0.0
