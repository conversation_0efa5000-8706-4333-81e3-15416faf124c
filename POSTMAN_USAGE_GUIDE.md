# Postman Collection Usage Guide

## 📁 File: `Postman_Collection_Fixed.json`

This is a **properly formatted** Postman collection that you can import directly into Postman.

## 🚀 Quick Start

### 1. Import the Collection
1. Open Postman
2. Click **"Import"** button
3. Select **"Upload Files"**
4. Choose `Postman_Collection_Fixed.json`
5. Click **"Import"**

### 2. Set Environment Variables
After importing, set these variables in Postman:
- `base_url`: `http://localhost:8080`
- `auth_token`: (will be auto-populated after login)
- `admin_token`: (will be auto-populated after admin login)

## 📋 Available Endpoints

### ✅ Health Check
- **GET** `/health` - Check if your Go backend is running

### 🔐 Authentication (5 endpoints)
- **POST** `/apis/auth/register` - Register new user
- **GET** `/apis/auth/get_login_otp/{userId}` - Get OTP for login
- **POST** `/apis/auth/otp_verify` - Verify OTP
- **POST** `/apis/auth/login` - Login with email & OTP
- **GET** `/apis/auth/profile` - Get user profile (requires auth token)

### ✈️ Flight Search (4 endpoints)
- **POST** `/apis/airports` - Search airports by name/city
- **POST** `/apis/search` - Search flights with detailed criteria
- **POST** `/apis/pricing` - Get flight pricing details
- **POST** `/apis/details` - Get comprehensive flight details

### 📋 Booking Management (2 endpoints)
- **POST** `/apis/create-booking` - Create flight booking (requires auth)
- **GET** `/apis/user-bookings` - Get user's bookings with pagination (requires auth)

### 💳 Payment Management (3 endpoints)
- **POST** `/apis/payment/initiate` - Start payment process (requires auth)
- **GET** `/apis/payment/{payment_id}/status` - Check payment status
- **POST** `/apis/payment/callback/razorpay` - Handle Razorpay payment callback

## 🔄 Testing Flow

### Step 1: Health Check
```
GET {{base_url}}/health
```
Expected: `200 OK` with health status

### Step 2: User Registration
```
POST {{base_url}}/apis/auth/register
Body: {
  "email": "<EMAIL>",
  "name": "Test User",
  "phone_number": "**********",
  "phone_country_code": "+91",
  "role": "CUSTOMER"
}
```

### Step 3: Get OTP & Login
1. Use the user ID from registration response
2. Get OTP: `GET {{base_url}}/apis/auth/get_login_otp/{userId}`
3. Login: `POST {{base_url}}/apis/auth/login?email=<EMAIL>&otp=123456`

### Step 4: Search Flights
```
POST {{base_url}}/apis/search
Body: {
  "SecType": "DOM",
  "FareType": "REGULAR",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "Source": "WEB",
  "Mode": "SS",
  "ClientID": "test_client",
  "TUI": "{{$randomUUID}}",
  "Trips": [
    {
      "Origin": "DEL",
      "Destination": "BOM", 
      "DepartureDate": "2024-12-25"
    }
  ]
}
```

### Step 5: Create Booking
Use the auth token from login and flight details from search to create a booking.

### Step 6: Process Payment
Initiate payment for the booking and handle the payment flow.

## 🔧 Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | Your Go backend URL | `http://localhost:8080` |
| `auth_token` | JWT token from login | Auto-populated |
| `admin_token` | Admin JWT token | Auto-populated |

## 📝 Sample Request Bodies

### User Registration
```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "phone_number": "**********",
  "phone_country_code": "+91",
  "role": "CUSTOMER"
}
```

### Flight Search
```json
{
  "SecType": "DOM",
  "FareType": "REGULAR",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "Source": "WEB",
  "Mode": "SS",
  "ClientID": "test_client",
  "IsMultipleCarrier": false,
  "IsRefundable": false,
  "TUI": "{{$randomUUID}}",
  "YTH": 0,
  "Trips": [
    {
      "Origin": "DEL",
      "Destination": "BOM",
      "DepartureDate": "2024-12-25"
    }
  ]
}
```

### Create Booking
```json
{
  "flight_booking": {
    "provider_info": {
      "code": "TJ"
    },
    "TUI": "search_tui_from_previous_search",
    "ADT": 1,
    "CHD": 0,
    "INF": 0,
    "NetAmount": 5000.0,
    "GrossAmount": 5500.0,
    "Hold": false
  },
  "Travellers": [
    {
      "Title": "Mr",
      "FName": "John",
      "LName": "Doe",
      "DOB": "1990-01-15",
      "Gender": "M",
      "PTC": "ADT",
      "PassportNo": "A12345678",
      "PassportExpiry": "2030-01-15",
      "Nationality": "IN"
    }
  ],
  "ContactInfo": {
    "email": "<EMAIL>",
    "phn_country_code": "+91",
    "phone": "**********"
  }
}
```

### Payment Initiation
```json
{
  "booking_reference": "BOOK123456",
  "amount": 5500.0,
  "currency": "INR",
  "method": "card",
  "gateway": "razorpay",
  "callback_url": "https://yourapp.com/payment/success",
  "cancel_url": "https://yourapp.com/payment/cancel",
  "customer_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+91**********"
  }
}
```

## ⚠️ Important Notes

1. **Authentication Required**: Endpoints marked with "requires auth" need the `Authorization: Bearer {{auth_token}}` header
2. **Sequential Testing**: Follow the flow: Register → Login → Search → Book → Pay
3. **Environment Variables**: Make sure to set `base_url` to your actual server URL
4. **Docker Running**: Ensure your Go backend is running via docker-compose
5. **Database Connection**: Verify MySQL and Redis are accessible

## 🐛 Troubleshooting

### Import Issues
- Make sure you're using `Postman_Collection_Fixed.json` (not the corrupted one)
- Try importing as "File" instead of "Link"

### Connection Issues
- Check if your Go backend is running: `http://localhost:8080/health`
- Verify Docker containers are up: `docker-compose ps`

### Authentication Issues
- Make sure to get a fresh OTP for each login attempt
- Check that the auth token is being saved to the environment variable

### API Errors
- Check the Go application logs for detailed error messages
- Verify request body format matches the expected structure

## 📞 Support

If you encounter issues:
1. Check the Go backend logs in your terminal
2. Verify all Docker services are running
3. Test the health endpoint first
4. Follow the sequential testing flow

Happy testing! 🚀
