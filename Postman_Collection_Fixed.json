{"info": {"_postman_id": "fast-travel-backend-go", "name": "Fast Travel Backend - Go API Collection", "description": "Complete API collection for the Go backend flight booking system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"phone_number\": \"**********\",\n  \"phone_country_code\": \"+91\",\n  \"role\": \"CUSTOMER\"\n}"}, "url": {"raw": "{{base_url}}/apis/auth/register", "host": ["{{base_url}}"], "path": ["apis", "auth", "register"]}}}, {"name": "Get Login OTP", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apis/auth/get_login_otp/1", "host": ["{{base_url}}"], "path": ["apis", "auth", "get_login_otp", "1"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/apis/auth/otp_verify", "host": ["{{base_url}}"], "path": ["apis", "auth", "otp_verify"]}}}, {"name": "Login with OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/apis/auth/login?email=<EMAIL>&otp=123456", "host": ["{{base_url}}"], "path": ["apis", "auth", "login"], "query": [{"key": "email", "value": "<EMAIL>"}, {"key": "otp", "value": "123456"}]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/apis/auth/profile", "host": ["{{base_url}}"], "path": ["apis", "auth", "profile"]}}}]}, {"name": "Flight Search", "item": [{"name": "Search Airports", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"Delhi\",\n  \"limit\": 10\n}"}, "url": {"raw": "{{base_url}}/apis/airports", "host": ["{{base_url}}"], "path": ["apis", "airports"]}}}, {"name": "Search Flights", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"SecType\": \"DOM\",\n  \"FareType\": \"REGULAR\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"Source\": \"WEB\",\n  \"Mode\": \"SS\",\n  \"ClientID\": \"test_client\",\n  \"IsMultipleCarrier\": false,\n  \"IsRefundable\": false,\n  \"TUI\": \"{{$randomUUID}}\",\n  \"YTH\": 0,\n  \"Trips\": [\n    {\n      \"Origin\": \"DEL\",\n      \"Destination\": \"BOM\",\n      \"DepartureDate\": \"2024-12-25\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/apis/search", "host": ["{{base_url}}"], "path": ["apis", "search"]}}}, {"name": "Get Flight Pricing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"Trips\": [\n    {\n      \"Amount\": 5000.0,\n      \"Index\": \"0\",\n      \"OrderID\": 1,\n      \"TUI\": \"search_tui_from_previous_search\"\n    }\n  ],\n  \"ClientID\": \"test_client\",\n  \"Mode\": \"SS\",\n  \"Options\": \"A\",\n  \"Source\": \"SF\",\n  \"TripType\": \"O\"\n}"}, "url": {"raw": "{{base_url}}/apis/pricing", "host": ["{{base_url}}"], "path": ["apis", "pricing"]}}}, {"name": "Get Flight Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FareId\": \"fare_id_from_search\",\n  \"TUI\": \"search_tui_from_previous_search\"\n}"}, "url": {"raw": "{{base_url}}/apis/details", "host": ["{{base_url}}"], "path": ["apis", "details"]}}}]}, {"name": "Booking Management", "item": [{"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"flight_booking\": {\n    \"provider_info\": {\n      \"code\": \"TJ\"\n    },\n    \"TUI\": \"search_tui_from_previous_search\",\n    \"ADT\": 1,\n    \"CHD\": 0,\n    \"INF\": 0,\n    \"NetAmount\": 5000.0,\n    \"GrossAmount\": 5500.0,\n    \"Hold\": false\n  },\n  \"Travellers\": [\n    {\n      \"Title\": \"Mr\",\n      \"FName\": \"<PERSON>\",\n      \"LName\": \"Do<PERSON>\",\n      \"DOB\": \"1990-01-15\",\n      \"Gender\": \"M\",\n      \"PTC\": \"ADT\",\n      \"PassportNo\": \"A12345678\",\n      \"PassportExpiry\": \"2030-01-15\",\n      \"Nationality\": \"IN\"\n    }\n  ],\n  \"ContactInfo\": {\n    \"email\": \"<EMAIL>\",\n    \"phn_country_code\": \"+91\",\n    \"phone\": \"**********\"\n  }\n}"}, "url": {"raw": "{{base_url}}/apis/create-booking", "host": ["{{base_url}}"], "path": ["apis", "create-booking"]}}}, {"name": "Get User Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/apis/user-bookings?page=1&limit=10", "host": ["{{base_url}}"], "path": ["apis", "user-bookings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Payment Management", "item": [{"name": "Initiate Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"booking_reference\": \"BOOK123456\",\n  \"amount\": 5500.0,\n  \"currency\": \"INR\",\n  \"method\": \"card\",\n  \"gateway\": \"razorpay\",\n  \"callback_url\": \"https://yourapp.com/payment/success\",\n  \"cancel_url\": \"https://yourapp.com/payment/cancel\",\n  \"customer_info\": {\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91**********\"\n  }\n}"}, "url": {"raw": "{{base_url}}/apis/payment/initiate", "host": ["{{base_url}}"], "path": ["apis", "payment", "initiate"]}}}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/apis/payment/PAY123456/status", "host": ["{{base_url}}"], "path": ["apis", "payment", "PAY123456", "status"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"razorpay_payment_id\": \"pay_123456789\",\n  \"razorpay_order_id\": \"order_123456789\",\n  \"razorpay_signature\": \"signature_hash\"\n}"}, "url": {"raw": "{{base_url}}/apis/payment/callback/razorpay", "host": ["{{base_url}}"], "path": ["apis", "payment", "callback", "razorpay"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8080"}, {"key": "auth_token", "value": ""}, {"key": "admin_token", "value": ""}]}