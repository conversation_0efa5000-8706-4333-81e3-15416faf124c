-- Initialize all databases for the Go backend application
-- Create databases if they don't exist
CREATE DATABASE IF NOT EXISTS tdb_auth;
CREATE DATABASE IF NOT EXISTS tdb_flight;
CREATE DATABASE IF NOT EXISTS tdb_booking;
-- Use the auth database and create basic tables
USE tdb_auth;
-- Users table for authentication (OTP-based, no password)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    phone_country_code VARCHAR(5) NOT NULL,
    otp VARCHAR(6),
    is_verified BOOLEAN DEFAULT FALSE,
    role ENUM('ADMIN', 'CUSTOMER') DEFAULT 'CUSTOMER',
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_deleted_at (deleted_at)
);
-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
-- Use the flight database
USE tdb_flight;
-- Airlines table
CREATE TABLE IF NOT EXISTS airlines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    country VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Airports table
CREATE TABLE IF NOT EXISTS airports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    timezone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Flights table
CREATE TABLE IF NOT EXISTS flights (
    id INT AUTO_INCREMENT PRIMARY KEY,
    flight_number VARCHAR(20) NOT NULL,
    airline_id INT NOT NULL,
    departure_airport_id INT NOT NULL,
    arrival_airport_id INT NOT NULL,
    departure_time DATETIME NOT NULL,
    arrival_time DATETIME NOT NULL,
    duration_minutes INT NOT NULL,
    aircraft_type VARCHAR(50),
    status VARCHAR(20) DEFAULT 'SCHEDULED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (airline_id) REFERENCES airlines(id),
    FOREIGN KEY (departure_airport_id) REFERENCES airports(id),
    FOREIGN KEY (arrival_airport_id) REFERENCES airports(id)
);
-- Use the booking database
USE tdb_booking;
-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    flight_id INT NOT NULL,
    passenger_count INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'PENDING',
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Passengers table
CREATE TABLE IF NOT EXISTS passengers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    passport_number VARCHAR(50),
    nationality VARCHAR(50),
    seat_number VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);
-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'PENDING',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);
-- Insert some sample data
USE tdb_auth;
INSERT IGNORE INTO users (
        email,
        name,
        phone_number,
        phone_country_code,
        role,
        is_verified,
        otp
    )
VALUES (
        '<EMAIL>',
        'Admin User',
        '1234567890',
        '+1',
        'ADMIN',
        TRUE,
        '123456'
    ),
    (
        '<EMAIL>',
        'Test User',
        '1234567891',
        '+1',
        'CUSTOMER',
        TRUE,
        '123456'
    ),
    (
        '<EMAIL>',
        'John Doe',
        '9876543210',
        '+91',
        'CUSTOMER',
        TRUE,
        '123456'
    );
USE tdb_flight;
INSERT IGNORE INTO airlines (code, name, country)
VALUES ('AI', 'Air India', 'India'),
    ('6E', 'IndiGo', 'India'),
    ('SG', 'SpiceJet', 'India'),
    ('UK', 'Vistara', 'India');
INSERT IGNORE INTO airports (code, name, city, country, timezone)
VALUES (
        'DEL',
        'Indira Gandhi International Airport',
        'Delhi',
        'India',
        'Asia/Kolkata'
    ),
    (
        'BOM',
        'Chhatrapati Shivaji Maharaj International Airport',
        'Mumbai',
        'India',
        'Asia/Kolkata'
    ),
    (
        'BLR',
        'Kempegowda International Airport',
        'Bangalore',
        'India',
        'Asia/Kolkata'
    ),
    (
        'MAA',
        'Chennai International Airport',
        'Chennai',
        'India',
        'Asia/Kolkata'
    ),
    (
        'CCU',
        'Netaji Subhas Chandra Bose International Airport',
        'Kolkata',
        'India',
        'Asia/Kolkata'
    );