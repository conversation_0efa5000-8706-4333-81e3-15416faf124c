version: '3.8'

services:
  # Go Backend Service (Development)
  gobackend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
      - "2345:2345"  # Delve debugger port
    depends_on:
      mysql-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    environment:
      # Server Configuration
      - PORT=8080
      - GIN_MODE=debug
      - DEBUG_MODE=true

      # Database Configuration
      - DATABASE_HOST=mysql-dev
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=Vishnu123
      - AUTH_DATABASE_NAME=tdb_auth
      - FLIGHT_DATABASE_NAME=tdb_flight
      - BOOKING_DATABASE_NAME=tdb_booking

      # Redis Configuration
      - REDIS_URL=redis://redis-dev:6379/0
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379

      # JWT Configuration
      - JWT_SECRET_KEY=dev_jwt_secret_key_for_development_only
      - JWT_EXPIRATION_HOURS=24
      - JWT_ISSUER=fast-travel-backend-go-dev

      # TripJack API Configuration (Test)
      - TRIPJACK_BASE_URL=https://apitest.tripjack.com/
      - TRIPJACK_API_KEY=6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9

      # Cache Configuration
      - MEMORY_CACHE_TTL_MINUTES=5
      - REDIS_CACHE_TTL_HOURS=1
      - FLIGHT_SEARCH_CACHE_TIMER=300
      - FLIGHT_PRICING_CACHE_TIMER=60

      # Performance Configuration
      - MAX_IDLE_CONNECTIONS=5
      - MAX_OPEN_CONNECTIONS=25
      - EXTERNAL_API_TIMEOUT_SECONDS=30

      # Development Features
      - ENABLE_SWAGGER=true
      - ENABLE_PROFILING=true
      - HOT_RELOAD_ENABLED=true
      - AUTO_MIGRATE_ENABLED=true
      - DEV_ENABLE_QUERY_LOGGING=true

      # CORS Configuration
      - CORS_ALLOWED_ORIGINS=http://localhost:4200,http://127.0.0.1:4200,http://***********:4200
      - CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - CORS_ALLOWED_HEADERS=Origin,Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization

      # Payment Gateway (Test)
      - RAZORPAY_KEY_ID=rzp_test_your_key_id
      - RAZORPAY_KEY_SECRET=your_test_key_secret

    volumes:
      - .:/app
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
    working_dir: /app
    command: |
      sh -c "
        echo 'Starting Go backend in development mode...'
        go mod download
        go run cmd/server/main.go
      "
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fasttravel-dev

  # MySQL Service (Development)
  mysql-dev:
    image: mysql:8.0
    container_name: fasttravel-mysql-dev
    environment:
      MYSQL_ROOT_PASSWORD: Vishnu123
      MYSQL_DATABASE: tdb_auth
      MYSQL_USER: fasttravel
      MYSQL_PASSWORD: Vishnu123
    ports:
      - "3306:3306"
    volumes:
      - mysql-dev-data:/var/lib/mysql
      - ./database/init_all_databases.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./database/dev-seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
      - ./database/dev-config.cnf:/etc/mysql/conf.d/dev-config.cnf
    command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=256M
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pVishnu123"]
      timeout: 20s
      retries: 10
      start_period: 40s
    networks:
      - fasttravel-dev

  # Redis Service (Development)
  redis-dev:
    image: redis:7-alpine
    container_name: fasttravel-redis-dev
    ports:
      - "6379:6379"
    command: |
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 60 1000
    volumes:
      - redis-dev-data:/data
      - ./redis/redis-dev.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - fasttravel-dev

  # Redis Commander (Development Tool)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: fasttravel-redis-commander
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    restart: unless-stopped
    profiles: ["tools"]
    networks:
      - fasttravel-dev

  # phpMyAdmin (Development Tool)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: fasttravel-phpmyadmin
    environment:
      PMA_HOST: mysql-dev
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: Vishnu123
      MYSQL_ROOT_PASSWORD: Vishnu123
    ports:
      - "8082:80"
    depends_on:
      - mysql-dev
    restart: unless-stopped
    profiles: ["tools"]
    networks:
      - fasttravel-dev

  # Mailhog (Email Testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: fasttravel-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    restart: unless-stopped
    profiles: ["tools"]
    networks:
      - fasttravel-dev

  # Prometheus (Monitoring)
  prometheus-dev:
    image: prom/prometheus:latest
    container_name: fasttravel-prometheus-dev
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-dev.yml:/etc/prometheus/prometheus.yml
      - prometheus-dev-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=7d'
    restart: unless-stopped
    profiles: ["monitoring"]
    networks:
      - fasttravel-dev

  # Grafana (Visualization)
  grafana-dev:
    image: grafana/grafana:latest
    container_name: fasttravel-grafana-dev
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-dev-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    profiles: ["monitoring"]
    networks:
      - fasttravel-dev

  # Jaeger (Distributed Tracing)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: fasttravel-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    restart: unless-stopped
    profiles: ["tracing"]
    networks:
      - fasttravel-dev

volumes:
  mysql-dev-data:
    driver: local
  redis-dev-data:
    driver: local
  prometheus-dev-data:
    driver: local
  grafana-dev-data:
    driver: local
  go-mod-cache:
    driver: local
  go-build-cache:
    driver: local

networks:
  fasttravel-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
