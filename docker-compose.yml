version: '3.8'

services:
  # Go Backend Service
  gobackend:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    environment:
      # Database Configuration
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=Vishnu123
      - AUTH_DATABASE_NAME=tdb_auth
      - FLIGHT_DATABASE_NAME=tdb_flight
      - BOOKING_DATABASE_NAME=tdb_booking

      # Redis Configuration
      - REDIS_URL=redis://redis:6379/0

      # JWT Configuration
      - JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_secure
      - JWT_EXPIRATION_HOURS=24
      - JWT_ISSUER=fast-travel-backend-go

      # Server Configuration
      - PORT=8080
      - GIN_MODE=release

      # Cache Configuration
      - MEMORY_CACHE_TTL_MINUTES=15
      - REDIS_CACHE_TTL_HOURS=1

      # Performance Configuration
      - MAX_IDLE_CONNECTIONS=10
      - MAX_OPEN_CONNECTIONS=100

      # TripJack API Configuration (add your credentials)
      - TRIPJACK_BASE_URL=https://apitest.tripjack.com/
      - TRIPJACK_API_KEY=6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9
      - TRIPJACK_USERNAME=your_tripjack_username
      - TRIPJACK_PASSWORD=your_tripjack_password
    volumes:
      - ./uploads:/root/uploads
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Service
  redis:
    image: "redis:7-alpine"
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # MySQL Service
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: Vishnu123
      MYSQL_DATABASE: tdb_auth
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_databases.sql:/docker-entrypoint-initdb.d/init_databases.sql
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pVishnu123" ]
      timeout: 20s
      retries: 10
      start_period: 40s

  # Optional: Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles: [ "monitoring" ]

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped
    profiles: [ "monitoring" ]

  # Optional: Nginx as reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - gobackend
    restart: unless-stopped
    profiles: [ "production" ]

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    driver: bridge
