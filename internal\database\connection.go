package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	Charset  string
}

type ConnectionPool struct {
	AuthDB    *gorm.DB
	FlightDB  *gorm.DB
	BookingDB *gorm.DB
	Redis     *redis.Client
	config    map[string]DatabaseConfig
}

var DB *ConnectionPool

// Initialize database connections with connection pooling
func InitializeConnections(configs map[string]DatabaseConfig, redisURL string) error {
	DB = &ConnectionPool{
		config: configs,
	}

	// Initialize MySQL connections
	if err := DB.initMySQLConnections(); err != nil {
		return fmt.Errorf("failed to initialize MySQL connections: %w", err)
	}

	// Initialize Redis connection
	if err := DB.initRedisConnection(redisURL); err != nil {
		return fmt.Errorf("failed to initialize Redis connection: %w", err)
	}

	log.Println("All database connections initialized successfully")
	return nil
}

func (cp *ConnectionPool) initMySQLConnections() error {
	// Auth Service Database
	authDSN := cp.buildDSN(cp.config["auth"])
	authDB, err := gorm.Open(mysql.Open(authDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to auth database: %w", err)
	}
	cp.AuthDB = authDB
	cp.configureConnectionPool(authDB, "auth")

	// Flight Service Database
	flightDSN := cp.buildDSN(cp.config["flight"])
	flightDB, err := gorm.Open(mysql.Open(flightDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to flight database: %w", err)
	}
	cp.FlightDB = flightDB
	cp.configureConnectionPool(flightDB, "flight")

	// Booking Service Database
	bookingDSN := cp.buildDSN(cp.config["booking"])
	bookingDB, err := gorm.Open(mysql.Open(bookingDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to booking database: %w", err)
	}
	cp.BookingDB = bookingDB
	cp.configureConnectionPool(bookingDB, "booking")

	return nil
}

func (cp *ConnectionPool) initRedisConnection(redisURL string) error {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	cp.Redis = redis.NewClient(opt)

	// Test Redis connection
	ctx := context.Background()
	_, err = cp.Redis.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	log.Println("Redis connection established successfully")
	return nil
}

func (cp *ConnectionPool) buildDSN(config DatabaseConfig) string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		config.User, config.Password, config.Host, config.Port, config.Database, config.Charset)
}

func (cp *ConnectionPool) configureConnectionPool(db *gorm.DB, serviceName string) {
	sqlDB, err := db.DB()
	if err != nil {
		log.Printf("Failed to get underlying sql.DB for %s: %v", serviceName, err)
		return
	}

	// Connection pool settings optimized for performance
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	sqlDB.SetConnMaxIdleTime(time.Minute * 30)

	log.Printf("%s database connection pool configured", serviceName)
}

// Health check for all database connections
func (cp *ConnectionPool) HealthCheck() map[string]bool {
	health := make(map[string]bool)

	// Check MySQL connections
	health["auth_db"] = cp.checkMySQLHealth(cp.AuthDB)
	health["flight_db"] = cp.checkMySQLHealth(cp.FlightDB)
	health["booking_db"] = cp.checkMySQLHealth(cp.BookingDB)

	// Check Redis connection
	ctx := context.Background()
	_, err := cp.Redis.Ping(ctx).Result()
	health["redis"] = err == nil

	return health
}

func (cp *ConnectionPool) checkMySQLHealth(db *gorm.DB) bool {
	sqlDB, err := db.DB()
	if err != nil {
		return false
	}
	return sqlDB.Ping() == nil
}

// Close all database connections
func (cp *ConnectionPool) Close() error {
	var errors []error

	// Close MySQL connections
	if sqlDB, err := cp.AuthDB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close auth DB: %w", err))
		}
	}

	if sqlDB, err := cp.FlightDB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close flight DB: %w", err))
		}
	}

	if sqlDB, err := cp.BookingDB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close booking DB: %w", err))
		}
	}

	// Close Redis connection
	if err := cp.Redis.Close(); err != nil {
		errors = append(errors, fmt.Errorf("failed to close Redis: %w", err))
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing connections: %v", errors)
	}

	log.Println("All database connections closed successfully")
	return nil
}
