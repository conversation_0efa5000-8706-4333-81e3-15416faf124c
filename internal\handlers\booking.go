package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"gobackend/internal/middleware"
	"gobackend/internal/models"
	"gobackend/internal/services"
)

// BookingHandler handles booking-related HTTP requests
type BookingHandler struct {
	bookingService *services.BookingService
	validator      *validator.Validate
}

// NewBookingHandler creates a new booking handler
func NewBookingHandler(bookingService *services.BookingService) *BookingHandler {
	return &BookingHandler{
		bookingService: bookingService,
		validator:      validator.New(),
	}
}

// CreateBooking handles booking creation requests
func (h *BookingHandler) CreateBooking(c *gin.Context) {
	var req models.BookingRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Use custom validation for flexible structure support
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Additional struct validation for travellers
	if err := h.validator.Struct(req); err != nil {
		// Only validate travellers array, ignore other validation errors
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			for _, validationError := range validationErrors {
				// Only fail on traveller validation errors
				if validationError.Field() == "Travellers" {
					c.JSON(http.StatusBadRequest, models.ErrorResponse{
						Error:   "Validation failed",
						Message: err.Error(),
						Code:    http.StatusBadRequest,
					})
					return
				}
			}
		}
	}

	// Get user ID from context
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "User not authenticated",
			Message: "Authentication required",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Create booking
	result, err := h.bookingService.CreateBooking(c.Request.Context(), req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Booking creation failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse{
		Success: true,
		Message: "Booking created successfully",
		Data:    result,
	})
}

// GetBookingByReference handles booking retrieval by reference
func (h *BookingHandler) GetBookingByReference(c *gin.Context) {
	bookingReference := c.Param("reference")
	if bookingReference == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Booking reference required",
			Message: "Booking reference parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get booking
	result, err := h.bookingService.GetBookingByReference(c.Request.Context(), bookingReference)
	if err != nil {
		if err.Error() == "booking not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Booking not found",
				Message: err.Error(),
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to retrieve booking",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Booking retrieved successfully",
		Data:    result,
	})
}

// GetUserBookings handles user bookings list retrieval
func (h *BookingHandler) GetUserBookings(c *gin.Context) {
	// Get user ID from context
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "User not authenticated",
			Message: "Authentication required",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 10
	}

	// Get user bookings
	result, err := h.bookingService.GetBookingsByUser(c.Request.Context(), userID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to retrieve bookings",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Set pagination headers
	c.Header("X-Total-Count", strconv.Itoa(result.Total))
	c.Header("X-Page", strconv.Itoa(result.Page))
	c.Header("X-Limit", strconv.Itoa(result.Limit))

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Bookings retrieved successfully",
		Data:    result,
	})
}

// UpdateBookingStatus handles booking status updates (admin only)
func (h *BookingHandler) UpdateBookingStatus(c *gin.Context) {
	bookingReference := c.Param("reference")
	if bookingReference == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Booking reference required",
			Message: "Booking reference parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req struct {
		Status string `json:"status" validate:"required,oneof=pending confirmed cancelled completed"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Convert string to BookingStatus
	var status models.BookingStatus
	switch req.Status {
	case "pending":
		status = models.BookingStatusPending
	case "confirmed":
		status = models.BookingStatusConfirmed
	case "cancelled":
		status = models.BookingStatusCancelled
	case "completed":
		status = models.BookingStatusCompleted
	default:
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid status",
			Message: "Invalid booking status provided",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Update booking status
	err := h.bookingService.UpdateBookingStatus(c.Request.Context(), bookingReference, status)
	if err != nil {
		if err.Error() == "booking not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Booking not found",
				Message: err.Error(),
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to update booking status",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Booking status updated successfully",
	})
}

// UpdatePaymentStatus handles payment status updates
func (h *BookingHandler) UpdatePaymentStatus(c *gin.Context) {
	bookingReference := c.Param("reference")
	if bookingReference == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Booking reference required",
			Message: "Booking reference parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req struct {
		PaymentStatus string `json:"payment_status" validate:"required,oneof=unpaid paid failed refunded"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Update payment status
	err := h.bookingService.UpdatePaymentStatus(c.Request.Context(), bookingReference, req.PaymentStatus)
	if err != nil {
		if err.Error() == "booking not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Booking not found",
				Message: err.Error(),
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to update payment status",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Payment status updated successfully",
	})
}

// CancelBooking handles booking cancellation
func (h *BookingHandler) CancelBooking(c *gin.Context) {
	bookingReference := c.Param("reference")
	if bookingReference == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Booking reference required",
			Message: "Booking reference parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req struct {
		Reason string `json:"reason,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		// Reason is optional, so ignore binding errors
	}

	// Update booking status to cancelled
	err := h.bookingService.UpdateBookingStatus(c.Request.Context(), bookingReference, models.BookingStatusCancelled)
	if err != nil {
		if err.Error() == "booking not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "Booking not found",
				Message: err.Error(),
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to cancel booking",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Booking cancelled successfully",
		Data: map[string]any{
			"booking_reference": bookingReference,
			"status":            "cancelled",
			"reason":            req.Reason,
		},
	})
}

// GetBookingStats handles booking statistics retrieval (admin only)
func (h *BookingHandler) GetBookingStats(c *gin.Context) {
	// This would typically query the database for booking statistics
	// For now, return sample statistics
	stats := map[string]any{
		"total_bookings":        1250,
		"pending_bookings":      45,
		"confirmed_bookings":    1100,
		"cancelled_bookings":    85,
		"completed_bookings":    20,
		"total_revenue":         2500000.00,
		"currency":              "INR",
		"average_booking_value": 2000.00,
		"bookings_today":        15,
		"bookings_this_week":    120,
		"bookings_this_month":   480,
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Booking statistics retrieved successfully",
		Data:    stats,
	})
}
