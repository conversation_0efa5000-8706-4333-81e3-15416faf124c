package models

import (
	"time"

	"gorm.io/gorm"
)

// UserRole represents the role of a user in the system
type UserRole string

const (
	UserRoleAdmin    UserRole = "ADMIN"
	UserRoleCustomer UserRole = "CUSTOMER"
)

// BaseModel provides common fields for all models
type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	IsDeleted bool           `gorm:"default:false" json:"is_deleted"`
}

// User represents the users table in the auth database
type User struct {
	BaseModel
	Email            string   `gorm:"type:varchar(100);not null;index" json:"email" validate:"required,email"`
	Name             string   `gorm:"type:varchar(50)" json:"name" validate:"required"`
	PhoneNumber      string   `gorm:"type:varchar(20)" json:"phone_number" validate:"required"`
	PhoneCountryCode string   `gorm:"type:varchar(5)" json:"phone_country_code" validate:"required"`
	OTP              string   `gorm:"type:varchar(6)" json:"otp,omitempty"`
	IsVerified       bool     `gorm:"default:false;not null" json:"is_verified"`
	Role             UserRole `gorm:"type:enum('ADMIN','CUSTOMER')" json:"role" validate:"required"`
}

// TableName specifies the table name for the User model
func (User) TableName() string {
	return "users"
}

// UserCreateRequest represents the request payload for creating a user
type UserCreateRequest struct {
	Email            string   `json:"email" validate:"required,email"`
	Name             string   `json:"name" validate:"required"`
	PhoneNumber      string   `json:"phone_number" validate:"required"`
	PhoneCountryCode string   `json:"phone_country_code" validate:"required"`
	Role             UserRole `json:"role" validate:"required"`
}

// UserLoginRequest represents the request payload for user login
type UserLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// UserOTPVerificationRequest represents the request payload for OTP verification
type UserOTPVerificationRequest struct {
	Email string `json:"email" validate:"required,email"`
	OTP   string `json:"otp" validate:"required,len=6"`
}

// UserResponse represents the response payload for user information
type UserResponse struct {
	ID               uint      `json:"id"`
	Email            string    `json:"email"`
	Name             string    `json:"name"`
	PhoneNumber      string    `json:"phone_number"`
	PhoneCountryCode string    `json:"phone_country_code"`
	IsVerified       bool      `json:"is_verified"`
	Role             UserRole  `json:"role"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// ToResponse converts a User model to UserResponse
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:               u.ID,
		Email:            u.Email,
		Name:             u.Name,
		PhoneNumber:      u.PhoneNumber,
		PhoneCountryCode: u.PhoneCountryCode,
		IsVerified:       u.IsVerified,
		Role:             u.Role,
		CreatedAt:        u.CreatedAt,
		UpdatedAt:        u.UpdatedAt,
	}
}

// AuthTokenResponse represents the response payload for authentication tokens
type AuthTokenResponse struct {
	AccessToken string       `json:"access_token"`
	TokenType   string       `json:"token_type"`
	ExpiresIn   int64        `json:"expires_in"`
	User        UserResponse `json:"user"`
}

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// SuccessResponse represents a standard success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}
