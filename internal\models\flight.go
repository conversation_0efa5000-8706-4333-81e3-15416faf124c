package models

import (
	"time"
)

// FlightSearchRequest represents the request payload for flight search (matches Python ScheduleBody)
type FlightSearchRequest struct {
	SecType           string      `json:"SecType"`
	FareType          string      `json:"FareType" validate:"required"`
	ADT               int         `json:"ADT" validate:"required,min=1"`
	CHD               int         `json:"CHD" validate:"min=0"`
	INF               int         `json:"INF" validate:"min=0"`
	Cabin             string      `json:"Cabin" validate:"required,oneof=E B PE F"`
	Source            string      `json:"Source"`
	Mode              string      `json:"Mode"`
	ClientID          string      `json:"ClientID"`
	IsMultipleCarrier bool        `json:"IsMultipleCarrier"`
	IsRefundable      bool        `json:"IsRefundable"`
	PreferredAirlines interface{} `json:"preferedAirlines"`
	TUI               string      `json:"TUI"`
	YTH               int         `json:"YTH"`
	Trips             []TripInfo  `json:"Trips" validate:"required,min=1"`
	Parameters        *Parameters `json:"Parameters"`
	PaymentType       string      `json:"PaymentType,omitempty"`
}

// TripInfo represents trip information in flight search (matches Python Trip)
type TripInfo struct {
	From         string  `json:"From" validate:"required,len=3"`
	FromArptName string  `json:"FromArptName,omitempty"`
	FromCity     string  `json:"FromCity,omitempty"`
	To           string  `json:"To" validate:"required,len=3"`
	ToArptName   string  `json:"ToArptName,omitempty"`
	ToCity       string  `json:"ToCity,omitempty"`
	OnwardDate   string  `json:"OnwardDate" validate:"required"`
	ReturnDate   *string `json:"ReturnDate,omitempty"`
	OrderId      *int    `json:"OrderId,omitempty"`
	TUI          string  `json:"TUI"`
}

// Parameters represents search parameters (matches Python Parameters)
type Parameters struct {
	Airlines        string `json:"Airlines"`
	GroupType       string `json:"GroupType"`
	Refundable      string `json:"Refundable"`
	IsDirect        bool   `json:"IsDirect"`
	IsStudentFare   bool   `json:"IsStudentFare"`
	IsNearbyAirport bool   `json:"IsNearbyAirport"`
}

// FlightSearchResponse represents the response payload for flight search (matches Python FlightResponse)
type FlightSearchResponse struct {
	ShPrice     bool           `json:"sh_price"`
	TUI         string         `json:"TUI"`
	Completed   bool           `json:"Completed"`
	CeilingInfo interface{}    `json:"CeilingInfo"`
	TripType    interface{}    `json:"TripType"`
	ElapsedTime string         `json:"ElapsedTime"`
	Notices     interface{}    `json:"Notices"`
	Msg         []string       `json:"Msg"`
	Code        int            `json:"Code"`
	Trips       []TripResponse `json:"Trips"`

	// Additional metadata (not in frontend model but useful for debugging)
	DataSource     string     `json:"data_source,omitempty"`
	CacheHit       bool       `json:"cache_hit,omitempty"`
	ResponseTimeMS float64    `json:"response_time_ms,omitempty"`
	CachedAt       *time.Time `json:"cached_at,omitempty"`
	CacheTTL       *int       `json:"cache_ttl_remaining,omitempty"`
}

// TripResponse represents a trip in the response (matches Python Trip)
type TripResponse struct {
	Journey []Journey `json:"Journey"`
}

// Journey represents a flight journey (matches Python Journey)
type Journey struct {
	Stops             int          `json:"Stops"`
	Seats             int          `json:"Seats"`
	ReturnIdentifier  interface{}  `json:"ReturnIdentifier"`
	Index             string       `json:"Index"`
	Provider          string       `json:"Provider"`
	FlightNo          string       `json:"FlightNo"`
	VAC               string       `json:"VAC"`
	MAC               string       `json:"MAC"`
	OAC               string       `json:"OAC"`
	ArrivalTime       string       `json:"ArrivalTime"`
	DepartureTime     string       `json:"DepartureTime"`
	FareClass         string       `json:"FareClass"`
	Duration          string       `json:"Duration"`
	GroupCount        int          `json:"GroupCount"`
	TotalFare         float64      `json:"TotalFare"`
	GrossFare         float64      `json:"GrossFare"`
	TotalCommission   float64      `json:"TotalCommission"`
	NetFare           float64      `json:"NetFare"`
	Hops              int          `json:"Hops"`
	Notice            string       `json:"Notice"`
	NoticeLink        string       `json:"NoticeLink"`
	NoticeType        string       `json:"NoticeType"`
	Refundable        string       `json:"Refundable"`
	Alliances         string       `json:"Alliances"`
	Amenities         string       `json:"Amenities"`
	Hold              bool         `json:"Hold"`
	Connections       []Connection `json:"Connections"`
	From              string       `json:"From"`
	To                string       `json:"To"`
	FromName          string       `json:"FromName"`
	ToName            string       `json:"ToName"`
	AirlineName       string       `json:"AirlineName"`
	AirCraft          string       `json:"AirCraft"`
	RBD               string       `json:"RBD"`
	Cabin             string       `json:"Cabin"`
	FBC               string       `json:"FBC"`
	FCBegin           interface{}  `json:"FCBegin"`
	FCEnd             interface{}  `json:"FCEnd"`
	FCType            string       `json:"FCType"`
	GFL               bool         `json:"GFL"`
	Promo             string       `json:"Promo"`
	Recommended       bool         `json:"Recommended"`
	Premium           bool         `json:"Premium"`
	JourneyKey        string       `json:"JourneyKey"`
	FareKey           string       `json:"FareKey"`
	PaxCategory       string       `json:"PaxCategory"`
	PrivateFareType   string       `json:"PrivateFareType"`
	DealKey           interface{}  `json:"DealKey"`
	VACAirlineLogo    string       `json:"VACAirlineLogo"`
	MACAirlineLogo    string       `json:"MACAirlineLogo"`
	OACAirlineLogo    string       `json:"OACAirlineLogo"`
	IsShowFareType    bool         `json:"isShowFareType"`
	CreatedJourneyKey string       `json:"CreatedJourneyKey,omitempty"`
	IsDisplay         bool         `json:"isDisplay,omitempty"`
	SubFlights        []SubFlight  `json:"SubFlights,omitempty"`
	ConnectionText    string       `json:"ConnectionText,omitempty"`
	IsSelect          bool         `json:"isSelect,omitempty"`
	IsVisible         bool         `json:"isVisible"`
	ChannelCode       string       `json:"ChannelCode,omitempty"`
	WpIndex           *string      `json:"WpIndex,omitempty"`
	IsSchedule        bool         `json:"IsSchedule,omitempty"`
	Inclusions        *Inclusions  `json:"Inclusions,omitempty"`
}

// Connection represents flight connection information (matches Python Connection)
type Connection struct {
	Airport        string `json:"Airport"`
	ArrAirportName string `json:"ArrAirportName"`
	Duration       string `json:"Duration"`
	MAC            string `json:"MAC"`
	Type           string `json:"Type"`
}

// SubFlight represents sub-flight information (matches Python SubFlight)
type SubFlight struct {
	Amount        *float64    `json:"Amount,omitempty"`
	Index         *string     `json:"Index,omitempty"`
	ChannelCode   *string     `json:"ChannelCode,omitempty"`
	Baggage       *string     `json:"Baggage,omitempty"`
	SeatSelection interface{} `json:"SeatSelection,omitempty"`
	Meal          *string     `json:"Meal,omitempty"`
	Refund        *string     `json:"Refund,omitempty"`
	FCType        *string     `json:"FCType,omitempty"`
	IsSelect      *bool       `json:"isSelect,omitempty"`
}

// Inclusions represents flight inclusions
type Inclusions struct {
	Baggage string `json:"Baggage,omitempty"`
	Meal    string `json:"Meal,omitempty"`
}

// FlightResult represents a single flight search result
type FlightResult struct {
	FlightID      string      `json:"FlightId"`
	FareID        string      `json:"FareId"`
	Airline       string      `json:"Airline"`
	FlightNumber  string      `json:"FlightNumber"`
	Origin        string      `json:"Origin"`
	Destination   string      `json:"Destination"`
	DepartureTime time.Time   `json:"DepartureTime"`
	ArrivalTime   time.Time   `json:"ArrivalTime"`
	Duration      string      `json:"Duration"`
	Aircraft      string      `json:"Aircraft"`
	Cabin         string      `json:"Cabin"`
	FareType      string      `json:"FareType"`
	Price         FlightPrice `json:"Price"`
	Availability  int         `json:"Availability"`
	Refundable    bool        `json:"Refundable"`
}

// FlightPrice represents pricing information for a flight
type FlightPrice struct {
	BaseFare  float64 `json:"BaseFare"`
	Taxes     float64 `json:"Taxes"`
	TotalFare float64 `json:"TotalFare"`
	Currency  string  `json:"Currency"`
}

// SearchListRequest represents the request for retrieving cached search results
type SearchListRequest struct {
	TUI string `json:"TUI" validate:"required"`
}

// PricingTrip represents a trip in the pricing request (matches Python backend)
type PricingTrip struct {
	Amount      float64 `json:"Amount" validate:"required"`
	Index       string  `json:"Index" validate:"required"`
	ChannelCode *string `json:"ChannelCode"`
	OrderID     int     `json:"OrderID" validate:"required"`
	TUI         string  `json:"TUI" validate:"required"`
}

// PricingRequest represents the request for flight pricing (matches Python backend exactly)
type PricingRequest struct {
	Trips    []PricingTrip `json:"Trips" validate:"required,min=1"`
	ClientID string        `json:"ClientID"`
	Mode     string        `json:"Mode"`
	Options  string        `json:"Options"`
	Source   string        `json:"Source"`
	TripType string        `json:"TripType" validate:"required"`
}

// Legacy PricingRequest for backward compatibility
type LegacyPricingRequest struct {
	FareID string                 `json:"FareId" validate:"required"`
	TUI    string                 `json:"TUI" validate:"required"`
	Trips  []TripInfo             `json:"Trips,omitempty"`
	ADT    int                    `json:"ADT" validate:"min=0"`
	CHD    int                    `json:"CHD" validate:"min=0"`
	INF    int                    `json:"INF" validate:"min=0"`
	Extra  map[string]interface{} `json:"extra,omitempty"`
}

// PricingResponse represents the response for flight pricing (matches Python backend structure)
type PricingResponse struct {
	TUI         string                `json:"TUI"`
	Code        string                `json:"Code"`
	Msg         []string              `json:"Msg"`
	From        string                `json:"From"`
	To          string                `json:"To"`
	FromName    string                `json:"FromName"`
	ToName      string                `json:"ToName"`
	OnwardDate  string                `json:"OnwardDate"`
	ADT         int                   `json:"ADT"`
	CHD         int                   `json:"CHD"`
	INF         int                   `json:"INF"`
	NetAmount   float64               `json:"NetAmount"`
	GrossAmount float64               `json:"GrossAmount"`
	FareType    string                `json:"FareType"`
	Trips       []TripPricingResponse `json:"Trips"`

	// Additional fields for compatibility
	Status         string                 `json:"status,omitempty"`
	ResponseTimeMS float64                `json:"response_time_ms,omitempty"`
	PricingDetails FlightPricingDetails   `json:"PricingDetails,omitempty"` // Keep for backward compatibility
	Extra          map[string]interface{} `json:"extra,omitempty"`
}

// TripPricingResponse represents a trip in the pricing response
type TripPricingResponse struct {
	Journey []JourneyPricingResponse `json:"Journey"`
}

// JourneyPricingResponse represents a journey in the pricing response
type JourneyPricingResponse struct {
	Provider  string                   `json:"Provider"`
	Stops     int                      `json:"Stops"`
	Segments  []SegmentPricingResponse `json:"Segments"`
	Offer     string                   `json:"Offer"`
	OrderID   int                      `json:"OrderID"`
	GrossFare float64                  `json:"GrossFare"`
	NetFare   float64                  `json:"NetFare"`
}

// SegmentPricingResponse represents a segment in the pricing response
type SegmentPricingResponse struct {
	Flight FlightPricingInfo `json:"Flight"`
	Fares  FarePricingInfo   `json:"Fares"`
}

// FlightPricingInfo represents flight information in pricing response
type FlightPricingInfo struct {
	FUID           string `json:"FUID"`
	VAC            string `json:"VAC"`
	MAC            string `json:"MAC"`
	OAC            string `json:"OAC"`
	Airline        string `json:"Airline"`
	FlightNo       string `json:"FlightNo"`
	DepAirportCode string `json:"DepAirportCode"`
	ArrAirportCode string `json:"ArrAirportCode"`
	DepAirportName string `json:"DepAirportName"`
	ArrAirportName string `json:"ArrAirportName"`
	DepDateTime    string `json:"DepDateTime"`
	ArrDateTime    string `json:"ArrDateTime"`
	Duration       string `json:"Duration"`
	CabinClass     string `json:"CabinClass"`
	BookingClass   string `json:"BookingClass"`
}

// FarePricingInfo represents fare information in pricing response
type FarePricingInfo struct {
	GrossFare float64       `json:"GrossFare"`
	NetFare   float64       `json:"NetFare"`
	TotalTax  float64       `json:"TotalTax"`
	PTCFare   []PTCFareInfo `json:"PTCFare"`
}

// PTCFareInfo represents passenger type code fare information
type PTCFareInfo struct {
	SSRDiscount         float64 `json:"SSRDiscount"`
	SSRMarkup           float64 `json:"SSRMarkup"`
	AgentMarkUp         float64 `json:"AgentMarkUp"`
	Ammendment          float64 `json:"Ammendment"`
	API                 float64 `json:"API"`
	CGST                float64 `json:"CGST"`
	CUTE                float64 `json:"CUTE"`
	Fare                float64 `json:"Fare"`
	GrossFare           float64 `json:"GrossFare"`
	IGST                float64 `json:"IGST"`
	K3                  float64 `json:"K3"`
	K7                  float64 `json:"K7"`
	NetFare             float64 `json:"NetFare"`
	OT                  string  `json:"OT"`
	OTT                 string  `json:"OTT"`
	PHF                 float64 `json:"PHF"`
	PSF                 float64 `json:"PSF"`
	PTC                 string  `json:"PTC"`
	RCF                 float64 `json:"RCF"`
	RCS                 float64 `json:"RCS"`
	ReissueCharge       float64 `json:"ReissueCharge"`
	SGST                float64 `json:"SGST"`
	ST                  float64 `json:"ST"`
	Tax                 float64 `json:"Tax"`
	TransactionFee      float64 `json:"TransactionFee"`
	UD                  float64 `json:"UD"`
	YQ                  float64 `json:"YQ"`
	YR                  float64 `json:"YR"`
	VATonServiceCharge  float64 `json:"VATonServiceCharge"`
	VATonTransactionFee float64 `json:"VATonTransactionFee"`
	JN                  float64 `json:"JN"`
}

// FlightPricingDetails represents detailed pricing information
type FlightPricingDetails struct {
	FareID     string                 `json:"FareId"`
	TotalFare  float64                `json:"TotalFare"`
	BaseFare   float64                `json:"BaseFare"`
	Taxes      float64                `json:"Taxes"`
	Currency   string                 `json:"Currency"`
	FareRules  []FareRule             `json:"FareRules"`
	Segments   []FlightSegment        `json:"Segments"`
	Passengers []PassengerFare        `json:"Passengers"`
	Extra      map[string]interface{} `json:"extra,omitempty"`
}

// FareRule represents fare rules and restrictions
type FareRule struct {
	RuleType    string `json:"RuleType"`
	Description string `json:"Description"`
	Penalty     string `json:"Penalty,omitempty"`
}

// FlightSegment represents a flight segment
type FlightSegment struct {
	SegmentID        string    `json:"SegmentId"`
	Airline          string    `json:"Airline"`
	FlightNumber     string    `json:"FlightNumber"`
	Origin           string    `json:"Origin"`
	Destination      string    `json:"Destination"`
	DepartureTime    time.Time `json:"DepartureTime"`
	ArrivalTime      time.Time `json:"ArrivalTime"`
	Duration         string    `json:"Duration"`
	Aircraft         string    `json:"Aircraft"`
	Cabin            string    `json:"Cabin"`
	BookingClass     string    `json:"BookingClass"`
	BaggageAllowance string    `json:"BaggageAllowance"`
}

// PassengerFare represents fare breakdown by passenger type
type PassengerFare struct {
	PassengerType string  `json:"PassengerType"`
	Count         int     `json:"Count"`
	BaseFare      float64 `json:"BaseFare"`
	Taxes         float64 `json:"Taxes"`
	TotalFare     float64 `json:"TotalFare"`
}

// DetailsRequest represents the request for flight details
type DetailsRequest struct {
	FareID string     `json:"FareId" validate:"required"`
	TUI    string     `json:"TUI" validate:"required"`
	Trips  []TripInfo `json:"Trips,omitempty"`
}

// ServiceRequest represents the request for flight services (SSR)
type ServiceRequest struct {
	FareID string     `json:"FareId" validate:"required"`
	TUI    string     `json:"TUI" validate:"required"`
	Trips  []TripInfo `json:"Trips,omitempty"`
}

// RulesRequest represents the request for fare rules
type RulesRequest struct {
	FareID string     `json:"FareId" validate:"required"`
	TUI    string     `json:"TUI" validate:"required"`
	Trips  []TripInfo `json:"Trips,omitempty"`
}

// AirportSearchRequest represents the request for airport search
type AirportSearchRequest struct {
	Query string `json:"query" validate:"required,min=2"`
	Limit int    `json:"limit" validate:"min=1,max=50"`
}

// Airport represents airport information
type Airport struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	City        string `json:"city"`
	Country     string `json:"country"`
	CountryCode string `json:"country_code"`
}

// AirportSearchResponse represents the response for airport search
type AirportSearchResponse struct {
	Query   string    `json:"query"`
	Results []Airport `json:"results"`
	Count   int       `json:"count"`
}

// TripJackSearchRequest represents the request format for TripJack API
type TripJackSearchRequest struct {
	SearchQuery TripJackSearchQuery `json:"searchQuery"`
}

// TripJackSearchQuery represents the search query for TripJack API
type TripJackSearchQuery struct {
	CabinClass        string                 `json:"cabinClass"`
	PaxInfo           TripJackPaxInfo        `json:"paxInfo"`
	RouteInfos        []TripJackRouteInfo    `json:"routeInfos"`
	SearchModifiers   map[string]interface{} `json:"searchModifiers"`
	PreferredAirlines []string               `json:"preferredAirline"`
}

// TripJackPaxInfo represents passenger information for TripJack API
type TripJackPaxInfo struct {
	Adult  string `json:"ADULT"`
	Child  string `json:"CHILD"`
	Infant string `json:"INFANT"`
}

// TripJackRouteInfo represents route information for TripJack API
type TripJackRouteInfo struct {
	FromCityOrAirport TripJackAirport `json:"fromCityOrAirport"`
	ToCityOrAirport   TripJackAirport `json:"toCityOrAirport"`
	TravelDate        string          `json:"travelDate"`
}

// TripJackAirport represents airport information for TripJack API
type TripJackAirport struct {
	Code string `json:"code"`
}

// SSRService represents Special Service Request information
type SSRService struct {
	Code        string  `json:"code"`
	Description string  `json:"description"`
	Price       float64 `json:"price"`
	Currency    string  `json:"currency"`
	Category    string  `json:"category"`
}

// SSRResponse represents the response for SSR services
type SSRResponse struct {
	TUI          string                  `json:"TUI"`
	FareID       string                  `json:"FareId"`
	Status       string                  `json:"status"`
	SSRServices  map[string][]SSRService `json:"SSRServices"`
	ResponseTime float64                 `json:"response_time_ms"`
}

// FareRulesResponse represents the response for fare rules
type FareRulesResponse struct {
	TUI          string     `json:"TUI"`
	FareID       string     `json:"FareId"`
	Status       string     `json:"status"`
	FareRules    []FareRule `json:"FareRules"`
	ResponseTime float64    `json:"response_time_ms"`
}

// FlightDetailsResponse represents detailed flight information response
type FlightDetailsResponse struct {
	TUI            string                 `json:"TUI"`
	FareID         string                 `json:"FareId"`
	Status         string                 `json:"status"`
	FlightDetails  FlightDetailInfo       `json:"FlightDetails"`
	PricingDetails FlightPricingDetails   `json:"PricingDetails"`
	ResponseTime   float64                `json:"response_time_ms"`
	Extra          map[string]interface{} `json:"extra,omitempty"`
}

// FlightDetailInfo represents detailed flight information
type FlightDetailInfo struct {
	Segments      []FlightSegment        `json:"segments"`
	TotalDuration string                 `json:"total_duration"`
	Stops         int                    `json:"stops"`
	Airlines      []string               `json:"airlines"`
	Aircraft      []string               `json:"aircraft"`
	Extra         map[string]interface{} `json:"extra,omitempty"`
}

// Enhanced FlightSegment with more details
type FlightSegmentDetailed struct {
	FlightSegment
	OperatingAirline   string                 `json:"operating_airline"`
	MarketingAirline   string                 `json:"marketing_airline"`
	CodeshareInfo      string                 `json:"codeshare_info,omitempty"`
	LayoverDuration    string                 `json:"layover_duration,omitempty"`
	TerminalInfo       TerminalInfo           `json:"terminal_info"`
	BaggageInfo        BaggageInfo            `json:"baggage_info"`
	CancellationPolicy string                 `json:"cancellation_policy"`
	Extra              map[string]interface{} `json:"extra,omitempty"`
}

// TerminalInfo represents terminal information
type TerminalInfo struct {
	DepartureTerminal string `json:"departure_terminal,omitempty"`
	ArrivalTerminal   string `json:"arrival_terminal,omitempty"`
}

// BaggageInfo represents baggage allowance information
type BaggageInfo struct {
	CheckedBaggage string `json:"checked_baggage"`
	CabinBaggage   string `json:"cabin_baggage"`
	PersonalItem   string `json:"personal_item,omitempty"`
}

// CacheMetadata represents cache-related metadata
type CacheMetadata struct {
	CacheKey   string     `json:"cache_key"`
	CachedAt   *time.Time `json:"cached_at,omitempty"`
	TTL        int        `json:"ttl,omitempty"`
	CacheLevel string     `json:"cache_level,omitempty"`
	HitCount   int        `json:"hit_count,omitempty"`
}

// PerformanceMetrics represents performance-related metrics
type PerformanceMetrics struct {
	ResponseTimeMS    float64 `json:"response_time_ms"`
	CacheHit          bool    `json:"cache_hit"`
	DataSource        string  `json:"data_source"`
	ExternalAPITime   float64 `json:"external_api_time_ms,omitempty"`
	ProcessingTime    float64 `json:"processing_time_ms,omitempty"`
	DatabaseQueryTime float64 `json:"database_query_time_ms,omitempty"`
}

// ErrorDetails represents detailed error information
type ErrorDetails struct {
	Code      string                 `json:"code"`
	Message   string                 `json:"message"`
	Details   string                 `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
	Extra     map[string]interface{} `json:"extra,omitempty"`
}

// TripJackBookingResponse represents TripJack booking API response
type TripJackBookingResponse struct {
	BookingID      string                 `json:"bookingId"`
	Status         string                 `json:"status"`
	PNR            string                 `json:"pnr,omitempty"`
	ResponseTimeMS float64                `json:"response_time_ms"`
	RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}

// TripJackConfirmResponse represents TripJack confirm booking API response
type TripJackConfirmResponse struct {
	BookingID      string                 `json:"bookingId"`
	Status         string                 `json:"status"`
	TicketNumbers  []string               `json:"ticketNumbers,omitempty"`
	ResponseTimeMS float64                `json:"response_time_ms"`
	RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}

// TripJackFareValidateResponse represents TripJack fare validation API response
type TripJackFareValidateResponse struct {
	FareID         string                 `json:"fareId"`
	IsValid        bool                   `json:"isValid"`
	ValidationMsg  string                 `json:"validationMsg,omitempty"`
	ResponseTimeMS float64                `json:"response_time_ms"`
	RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}

// SeatMapResponse represents seat map API response
type SeatMapResponse struct {
	FareID         string                 `json:"fareId"`
	Status         string                 `json:"status"`
	SeatMap        map[string]interface{} `json:"seatMap"`
	ResponseTimeMS float64                `json:"response_time_ms"`
}

// BookingDetailsResponse represents TripJack booking details API response
type BookingDetailsResponse struct {
	BookingID      string                 `json:"bookingId"`
	Status         string                 `json:"status"`
	BookingDetails map[string]interface{} `json:"bookingDetails"`
	ResponseTimeMS float64                `json:"response_time_ms"`
}

// ReleasePNRResponse represents TripJack release PNR API response
type ReleasePNRResponse struct {
	BookingID      string                 `json:"bookingId"`
	Status         string                 `json:"status"`
	Message        string                 `json:"message"`
	ResponseTimeMS float64                `json:"response_time_ms"`
	RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}

// AmendmentChargesResponse represents TripJack amendment charges API response
type AmendmentChargesResponse struct {
	BookingID      string                 `json:"bookingId"`
	AmendmentType  string                 `json:"amendmentType"`
	Charges        map[string]interface{} `json:"charges"`
	ResponseTimeMS float64                `json:"response_time_ms"`
}

// AmendmentRequest represents amendment request data
type AmendmentRequest struct {
	BookingID     string                 `json:"bookingId"`
	AmendmentType string                 `json:"amendmentType"`
	AmendmentData map[string]interface{} `json:"amendmentData"`
	Passengers    []interface{}          `json:"passengers,omitempty"`
}

// SubmitAmendmentResponse represents TripJack submit amendment API response
type SubmitAmendmentResponse struct {
	BookingID        string                 `json:"bookingId"`
	AmendmentID      string                 `json:"amendmentId"`
	Status           string                 `json:"status"`
	Message          string                 `json:"message"`
	AmendmentDetails map[string]interface{} `json:"amendmentDetails"`
	ResponseTimeMS   float64                `json:"response_time_ms"`
}

// AmendmentDetailsResponse represents TripJack amendment details API response
type AmendmentDetailsResponse struct {
	AmendmentID      string                 `json:"amendmentId"`
	Status           string                 `json:"status"`
	AmendmentDetails map[string]interface{} `json:"amendmentDetails"`
	ResponseTimeMS   float64                `json:"response_time_ms"`
}

// UserDetailResponse represents TripJack user detail API response
type UserDetailResponse struct {
	UserID         string                 `json:"userId"`
	Status         string                 `json:"status"`
	UserDetails    map[string]interface{} `json:"userDetails"`
	ResponseTimeMS float64                `json:"response_time_ms"`
}
