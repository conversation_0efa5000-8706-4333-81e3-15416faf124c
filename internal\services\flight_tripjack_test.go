package services

import (
	"context"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gobackend/internal/models"
	"gobackend/pkg/cache"
)

// setupTripJackTestServer creates a mock TripJack server for testing
func setupTripJackTestServer() *httptest.Server {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Mock TripJack Search API
	router.POST("/fms/v1/air-search-all", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Validate TripJack search request format
		searchQuery, exists := req["searchQuery"].(map[string]interface{})
		if !exists {
			c.<PERSON>(400, gin.H{"error": "Missing searchQuery"})
			return
		}

		// Validate required fields
		if _, exists := searchQuery["cabinClass"]; !exists {
			c.J<PERSON>(400, gin.H{"error": "Missing cabinClass"})
			return
		}

		paxInfo, exists := searchQuery["paxInfo"]
		if !exists {
			c.JSON(400, gin.H{"error": "Missing paxInfo"})
			return
		}

		// Validate passenger count
		if pax, ok := paxInfo.(map[string]interface{}); ok {
			if adultStr, ok := pax["ADULT"].(string); ok {
				if adultStr == "0" {
					c.JSON(400, gin.H{"error": "Invalid passenger count - no adults"})
					return
				}
			}
		}

		routeInfos, exists := searchQuery["routeInfos"]
		if !exists {
			c.JSON(400, gin.H{"error": "Missing routeInfos"})
			return
		}

		// Validate route infos
		if routes, ok := routeInfos.([]interface{}); ok {
			if len(routes) == 0 {
				c.JSON(400, gin.H{"error": "Empty routeInfos"})
				return
			}
		} else {
			c.JSON(400, gin.H{"error": "Invalid routeInfos format"})
			return
		}

		// Mock TripJack search response
		response := map[string]interface{}{
			"searchResult": map[string]interface{}{
				"tripInfos": map[string]interface{}{
					"ONWARD": []map[string]interface{}{
						{
							"sI": []map[string]interface{}{
								{
									"fD": map[string]interface{}{
										"aI": map[string]interface{}{
											"code":  "AI",
											"name":  "Air India",
											"isLcc": false,
										},
										"fN": "439",
										"eT": "32B",
									},
									"stops":    0,
									"duration": 170,
									"da": map[string]interface{}{
										"code":        "DEL",
										"name":        "Delhi intl airport",
										"cityCode":    "DEL",
										"city":        "Delhi",
										"country":     "India",
										"countryCode": "IN",
									},
									"aa": map[string]interface{}{
										"code":        "BOM",
										"name":        "Mumbai Airport",
										"cityCode":    "BOM",
										"city":        "Mumbai",
										"country":     "India",
										"countryCode": "IN",
									},
									"dt":   "2025-06-12T06:05",
									"at":   "2025-06-12T08:55",
									"iand": false,
									"isRs": false,
									"sN":   0,
								},
							},
							"totalPriceList": []map[string]interface{}{
								{
									"fd": map[string]interface{}{
										"ADULT": map[string]interface{}{
											"fC": map[string]interface{}{
												"TF":  2343,
												"TAF": 653,
												"BF":  1690,
												"NF":  2343,
											},
											"afC": map[string]interface{}{
												"TAF": map[string]interface{}{
													"YR":   170,
													"AGST": 93,
													"OT":   390,
												},
											},
											"sR": 9,
											"bI": map[string]interface{}{
												"iB": "25 Kilograms",
											},
											"rT": 1,
											"cc": "ECONOMY",
											"cB": "S",
											"fB": "SAP30",
										},
									},
									"fareIdentifier": "PUBLISHED",
									"id":             "test-fare-id-123",
									"msri":           []interface{}{},
								},
							},
						},
					},
				},
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Review API
	router.POST("/fms/v1/review", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Validate TripJack review request format
		priceIds, exists := req["priceIds"]
		if !exists {
			c.JSON(400, gin.H{"error": "Missing priceIds"})
			return
		}

		// Check if priceIds is empty
		priceList, ok := priceIds.([]interface{})
		if !ok {
			c.JSON(400, gin.H{"error": "Invalid priceIds format"})
			return
		}

		if len(priceList) == 0 {
			c.JSON(400, gin.H{"error": "Empty priceIds"})
			return
		}

		// Check if all elements are empty strings
		allEmpty := true
		for _, item := range priceList {
			if str, ok := item.(string); !ok || str != "" {
				allEmpty = false
				break
			}
		}

		if allEmpty {
			c.JSON(400, gin.H{"error": "Empty priceIds"})
			return
		}

		// Mock TripJack review response
		response := map[string]interface{}{
			"tripInfos": []map[string]interface{}{
				{
					"sI": []map[string]interface{}{
						{
							"fD": map[string]interface{}{
								"aI": map[string]interface{}{
									"code": "AI",
									"name": "Air India",
								},
								"fN": "439",
							},
							"da": map[string]interface{}{
								"code": "DEL",
								"name": "Delhi",
							},
							"aa": map[string]interface{}{
								"code": "BOM",
								"name": "Mumbai",
							},
							"dt": "2025-06-12T06:05",
							"at": "2025-06-12T08:55",
						},
					},
					"totalPriceList": []map[string]interface{}{
						{
							"fd": map[string]interface{}{
								"ADULT": map[string]interface{}{
									"fC": map[string]interface{}{
										"TF": 2343,
										"BF": 1690,
									},
								},
							},
							"id": "test-fare-id-123",
						},
					},
				},
			},
			"bookingId": "TJS123456789",
		}

		c.JSON(200, response)
	})

	// Mock TripJack FareRule API
	router.POST("/fms/v2/farerule", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack fare rules response
		response := map[string]interface{}{
			"fareRules": []map[string]interface{}{
				{
					"ruleType":    "CANCELLATION",
					"description": "Cancellation charges apply",
					"penalty":     "INR 3000",
				},
				{
					"ruleType":    "CHANGE",
					"description": "Change charges apply",
					"penalty":     "INR 2500",
				},
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Book API
	router.POST("/oms/v1/air/book", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Validate TripJack booking request format
		priceIds, exists := req["priceIds"]
		if !exists {
			c.JSON(400, gin.H{"error": "Missing priceIds"})
			return
		}

		// Check if priceIds is empty
		if priceList, ok := priceIds.([]interface{}); !ok || len(priceList) == 0 {
			c.JSON(400, gin.H{"error": "Empty priceIds"})
			return
		}

		// Check for passengers
		passengers, exists := req["passengers"]
		if !exists {
			c.JSON(400, gin.H{"error": "Missing passengers"})
			return
		}

		// Check if passengers is empty
		if passengerList, ok := passengers.([]interface{}); !ok || len(passengerList) == 0 {
			c.JSON(400, gin.H{"error": "Empty passengers"})
			return
		}

		// Mock TripJack booking response
		response := map[string]interface{}{
			"bookingId": "TJS123456789",
			"status":    "SUCCESS",
			"pnr":       "ABC123",
			"order": map[string]interface{}{
				"bookingId": "TJS123456789",
				"status":    "SUCCESS",
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Fare Validate API
	router.POST("/oms/v1/air/fare-validate", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack fare validation response
		response := map[string]interface{}{
			"isValid":       true,
			"validationMsg": "Fare is valid",
		}

		c.JSON(200, response)
	})

	// Mock TripJack Confirm Book API
	router.POST("/oms/v1/air/confirm-book", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack confirm booking response
		response := map[string]interface{}{
			"bookingId": "TJS123456789",
			"status":    "CONFIRMED",
			"pnr":       "ABC123",
		}

		c.JSON(200, response)
	})

	// Mock TripJack Booking Details API
	router.POST("/oms/v1/booking-details", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack booking details response
		response := map[string]interface{}{
			"order": map[string]interface{}{
				"bookingId": "TJS123456789",
				"status":    "SUCCESS",
				"amount":    2343.0,
			},
			"itemInfos": map[string]interface{}{
				"AIR": map[string]interface{}{
					"tripInfos": []interface{}{},
				},
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Release PNR API
	router.POST("/oms/v1/air/unhold", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack release PNR response
		response := map[string]interface{}{
			"bookingId": "TJS123456789",
			"status":    "SUCCESS",
			"message":   "PNR released successfully",
		}

		c.JSON(200, response)
	})

	// Mock TripJack Seat Service API
	router.POST("/fms/v1/seat", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack seat map response
		response := map[string]interface{}{
			"seatMap": map[string]interface{}{
				"segments": []map[string]interface{}{
					{
						"segmentId": "DEL-BOM-1",
						"seats": []map[string]interface{}{
							{
								"seatNumber": "1A",
								"available":  true,
								"price":      500.0,
								"type":       "WINDOW",
							},
							{
								"seatNumber": "1B",
								"available":  true,
								"price":      500.0,
								"type":       "MIDDLE",
							},
						},
					},
				},
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Get Amendment Charges API
	router.POST("/oms/v1/air/amendment/amendment-charges", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack amendment charges response
		response := map[string]interface{}{
			"bookingId":     "TJS123456789",
			"amendmentType": "DATE_CHANGE",
			"charges": map[string]interface{}{
				"airlineCharges": 2500.0,
				"agentCharges":   500.0,
				"totalCharges":   3000.0,
				"currency":       "INR",
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Submit Amendment API
	router.POST("/oms/v1/air/amendment/submit-amendment", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack submit amendment response
		response := map[string]interface{}{
			"bookingId":   "TJS123456789",
			"amendmentId": "AMD123456789",
			"status":      "SUCCESS",
			"message":     "Amendment submitted successfully",
			"amendmentDetails": map[string]interface{}{
				"type":       "DATE_CHANGE",
				"newDate":    "2025-06-15",
				"charges":    3000.0,
				"refundable": false,
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack Amendment Details API
	router.POST("/oms/v1/air/amendment/amendment-details", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack amendment details response
		response := map[string]interface{}{
			"amendmentId": "AMD123456789",
			"status":      "CONFIRMED",
			"amendmentDetails": map[string]interface{}{
				"bookingId":     "TJS123456789",
				"amendmentType": "DATE_CHANGE",
				"originalDate":  "2025-06-12",
				"newDate":       "2025-06-15",
				"charges":       3000.0,
				"processedAt":   "2025-01-08T10:30:00Z",
			},
		}

		c.JSON(200, response)
	})

	// Mock TripJack User Detail API
	router.POST("/ums/v1/user-detail", func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request"})
			return
		}

		// Mock TripJack user details response
		response := map[string]interface{}{
			"userId": "USER123456789",
			"status": "SUCCESS",
			"userDetails": map[string]interface{}{
				"name":          "Test User",
				"email":         "<EMAIL>",
				"phone":         "**********",
				"walletBalance": 5000.0,
				"currency":      "INR",
				"kycStatus":     "VERIFIED",
				"accountType":   "AGENT",
			},
		}

		c.JSON(200, response)
	})

	return httptest.NewServer(router)
}

// createTestFlightService creates a FlightService instance for testing
func createTestFlightService(baseURL string) *FlightService {
	cfg := TripJackConfig{
		BaseURL: baseURL + "/",
		APIKey:  "test-api-key",
		Timeout: 30 * time.Second,
	}

	// Create a mock cache service with proper initialization
	cacheConfig := cache.CacheConfig{
		RedisClient:     nil, // No Redis for testing
		MemoryCacheTTL:  5 * time.Minute,
		RedisCacheTTL:   30 * time.Minute,
		CleanupInterval: 10 * time.Minute,
	}

	// Create multi-layer cache (will only use memory cache since Redis is nil)
	multiLayerCache := cache.NewMultiLayerCache(cacheConfig)
	mockCacheService := cache.NewFlightCacheService(multiLayerCache)

	return NewFlightService(mockCacheService, cfg)
}

// TestTripJackSearchFlights tests the TripJack search API integration
func TestTripJackSearchFlights(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	// Test valid search request
	t.Run("Valid Search Request", func(t *testing.T) {
		request := models.FlightSearchRequest{
			FareType: "REGULAR",
			ADT:      1,
			CHD:      0,
			INF:      0,
			Cabin:    "E",
			Trips: []models.TripInfo{
				{
					From:       "DEL",
					To:         "BOM",
					OnwardDate: "2025-06-12",
				},
			},
		}

		response, err := service.SearchFlights(context.Background(), request)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.NotEmpty(t, response.Trips)
		assert.Equal(t, 1, len(response.Trips))

		trip := response.Trips[0]
		assert.NotEmpty(t, trip.Journey)
		assert.Equal(t, 1, len(trip.Journey))

		journey := trip.Journey[0]
		assert.Equal(t, "AI", journey.VAC)
		assert.Equal(t, "Air India", journey.AirlineName)
		assert.Equal(t, "DEL", journey.From)
		assert.Equal(t, "BOM", journey.To)
		assert.NotEmpty(t, journey.FareKey)
	})

	// Test invalid search request
	t.Run("Invalid Search Request", func(t *testing.T) {
		request := models.FlightSearchRequest{
			// Missing required fields
		}

		response, err := service.SearchFlights(context.Background(), request)
		require.NoError(t, err) // Service handles errors gracefully
		require.NotNil(t, response)

		// Should return background task response
		assert.Equal(t, 202, response.Code) // Accepted - processing
		assert.False(t, response.Completed)
		assert.Equal(t, "background_task", response.DataSource)

		// Check notices if they exist
		if response.Notices != nil {
			if notices, ok := response.Notices.([]interface{}); ok && len(notices) > 0 {
				if notice, ok := notices[0].(string); ok {
					assert.Contains(t, notice, "Search in progress")
				}
			}
		}
	})
}

// TestTripJackGetFlightPricing tests the TripJack review API integration
func TestTripJackGetFlightPricing(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Pricing Request", func(t *testing.T) {
		request := models.PricingRequest{
			Trips: []models.PricingTrip{
				{
					Amount:  5000.0,
					Index:   "test-fare-id-123",
					OrderID: 1,
					TUI:     "test-tui",
				},
			},
			ClientID: "",
			Mode:     "SS",
			Options:  "A",
			Source:   "SF",
			TripType: "O",
		}

		response, err := service.GetFlightPricing(context.Background(), request)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.NotEmpty(t, response.TUI)
		assert.Equal(t, "test-tui", response.TUI)
		assert.Equal(t, "success", response.Status)
	})

	t.Run("Invalid Pricing Request", func(t *testing.T) {
		request := models.PricingRequest{
			// Missing required fields
		}

		_, err := service.GetFlightPricing(context.Background(), request)
		assert.Error(t, err)
	})
}

// TestTripJackGetFareRules tests the TripJack fare rules API integration
func TestTripJackGetFareRules(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Fare Rules Request", func(t *testing.T) {
		fareID := "test-fare-id-123"

		response, err := service.GetFareRules(context.Background(), fareID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.NotEmpty(t, response.FareRules)
		assert.GreaterOrEqual(t, len(response.FareRules), 1)

		rule := response.FareRules[0]
		assert.NotEmpty(t, rule.RuleType)
		assert.NotEmpty(t, rule.Description)
	})
}

// TestTripJackBookFlight tests the TripJack booking API integration
func TestTripJackBookFlight(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Booking Request", func(t *testing.T) {
		request := models.BookingRequest{
			FareID: "test-fare-id-123",
			TUI:    "test-tui",
			Trips: []models.TripInfo{
				{
					From:       "DEL",
					To:         "BOM",
					OnwardDate: "2025-06-12",
				},
			},
			Travellers: []models.TravellerRequest{
				{
					Title:         "Mr",
					FirstName:     "Test",
					LastName:      "User",
					Gender:        "male",
					DateOfBirth:   "1990-01-01",
					PassengerType: "ADT",
				},
			},
			ContactInfo: models.ContactInfoRequest{
				Email:       "<EMAIL>",
				PhoneNumber: "**********",
				CountryCode: "+91",
				FirstName:   "Test",
				LastName:    "User",
			},
		}

		response, err := service.BookFlight(context.Background(), request)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.NotEmpty(t, response.BookingID)
		assert.Equal(t, "SUCCESS", response.Status)
	})
}

// TestTripJackValidateFare tests the TripJack fare validation API integration
func TestTripJackValidateFare(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Fare Validation Request", func(t *testing.T) {
		fareID := "test-fare-id-123"

		response, err := service.ValidateFare(context.Background(), fareID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.True(t, response.IsValid)
		assert.NotEmpty(t, response.ValidationMsg)
	})
}

// TestTripJackConfirmBooking tests the TripJack confirm booking API integration
func TestTripJackConfirmBooking(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Confirm Booking Request", func(t *testing.T) {
		bookingID := "TJS123456789"

		response, err := service.ConfirmBooking(context.Background(), bookingID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, "TJS123456789", response.BookingID)
		assert.Equal(t, "CONFIRMED", response.Status)
	})
}

// TestTripJackGetBookingDetails tests the TripJack booking details API integration
func TestTripJackGetBookingDetails(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Booking Details Request", func(t *testing.T) {
		bookingID := "TJS123456789"

		response, err := service.GetBookingDetails(context.Background(), bookingID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, "TJS123456789", response.BookingID)
		assert.Equal(t, "SUCCESS", response.Status)
	})
}

// TestTripJackGetSeatMap tests the TripJack seat map API integration
func TestTripJackGetSeatMap(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Seat Map Request", func(t *testing.T) {
		fareID := "test-fare-id-123"

		response, err := service.GetSeatMap(context.Background(), fareID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, fareID, response.FareID)
		assert.Equal(t, "success", response.Status)
		assert.NotNil(t, response.SeatMap)
	})
}

// TestTripJackReleasePNR tests the TripJack release PNR API integration
func TestTripJackReleasePNR(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Release PNR Request", func(t *testing.T) {
		bookingID := "TJS123456789"

		response, err := service.ReleasePNR(context.Background(), bookingID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, bookingID, response.BookingID)
		assert.Equal(t, "SUCCESS", response.Status)
		assert.NotEmpty(t, response.Message)
	})
}

// TestTripJackGetAmendmentCharges tests the TripJack amendment charges API integration
func TestTripJackGetAmendmentCharges(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Amendment Charges Request", func(t *testing.T) {
		bookingID := "TJS123456789"
		amendmentType := "DATE_CHANGE"

		response, err := service.GetAmendmentCharges(context.Background(), bookingID, amendmentType)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, bookingID, response.BookingID)
		assert.Equal(t, amendmentType, response.AmendmentType)
		assert.NotNil(t, response.Charges)
	})
}

// TestTripJackSubmitAmendment tests the TripJack submit amendment API integration
func TestTripJackSubmitAmendment(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Submit Amendment Request", func(t *testing.T) {
		amendmentReq := models.AmendmentRequest{
			BookingID:     "TJS123456789",
			AmendmentType: "DATE_CHANGE",
			AmendmentData: map[string]interface{}{
				"newDate": "2025-06-15",
			},
		}

		response, err := service.SubmitAmendment(context.Background(), amendmentReq)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, amendmentReq.BookingID, response.BookingID)
		assert.NotEmpty(t, response.AmendmentID)
		assert.Equal(t, "SUCCESS", response.Status)
	})
}

// TestTripJackIntegrationFlow tests a complete flow of TripJack API calls
func TestTripJackIntegrationFlow(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Complete TripJack Integration Flow", func(t *testing.T) {
		// Step 1: Search flights
		searchReq := models.FlightSearchRequest{
			FareType: "REGULAR",
			ADT:      1,
			CHD:      0,
			INF:      0,
			Cabin:    "E",
			Trips: []models.TripInfo{
				{
					From:       "DEL",
					To:         "BOM",
					OnwardDate: "2025-06-12",
				},
			},
		}

		searchResponse, err := service.SearchFlights(context.Background(), searchReq)
		require.NoError(t, err)
		require.NotNil(t, searchResponse)
		require.NotEmpty(t, searchResponse.Trips)

		// Step 2: Get pricing for a flight
		fareID := "test-fare-id-123"
		pricingReq := models.PricingRequest{
			Trips: []models.PricingTrip{
				{
					Amount:  5000.0,
					Index:   fareID,
					OrderID: 1,
					TUI:     searchResponse.TUI,
				},
			},
			ClientID: "",
			Mode:     "SS",
			Options:  "A",
			Source:   "SF",
			TripType: "O",
		}

		pricingResponse, err := service.GetFlightPricing(context.Background(), pricingReq)
		require.NoError(t, err)
		require.NotNil(t, pricingResponse)

		// Step 3: Get fare rules
		fareRulesResponse, err := service.GetFareRules(context.Background(), fareID)
		require.NoError(t, err)
		require.NotNil(t, fareRulesResponse)

		// Step 4: Validate fare
		validateResponse, err := service.ValidateFare(context.Background(), fareID)
		require.NoError(t, err)
		require.NotNil(t, validateResponse)
		assert.True(t, validateResponse.IsValid)

		// Step 5: Create booking
		bookingReq := models.BookingRequest{
			FareID: fareID,
			TUI:    searchResponse.TUI,
			Trips:  searchReq.Trips,
			Travellers: []models.TravellerRequest{
				{
					Title:         "Mr",
					FirstName:     "Test",
					LastName:      "User",
					Gender:        "male",
					DateOfBirth:   "1990-01-01",
					PassengerType: "ADT",
				},
			},
			ContactInfo: models.ContactInfoRequest{
				Email:       "<EMAIL>",
				PhoneNumber: "**********",
				CountryCode: "+91",
				FirstName:   "Test",
				LastName:    "User",
			},
		}

		bookingResponse, err := service.BookFlight(context.Background(), bookingReq)
		require.NoError(t, err)
		require.NotNil(t, bookingResponse)

		// Step 6: Confirm booking
		confirmResponse, err := service.ConfirmBooking(context.Background(), bookingResponse.BookingID)
		require.NoError(t, err)
		require.NotNil(t, confirmResponse)

		// Step 7: Get booking details
		detailsResponse, err := service.GetBookingDetails(context.Background(), bookingResponse.BookingID)
		require.NoError(t, err)
		require.NotNil(t, detailsResponse)

		// Validate the complete flow
		assert.Equal(t, bookingResponse.BookingID, confirmResponse.BookingID)
		assert.Equal(t, bookingResponse.BookingID, detailsResponse.BookingID)
		assert.Equal(t, "SUCCESS", bookingResponse.Status)
		assert.Equal(t, "CONFIRMED", confirmResponse.Status)
		assert.Equal(t, "SUCCESS", detailsResponse.Status)
	})
}

// TestTripJackGetAmendmentDetails tests the TripJack amendment details API integration
func TestTripJackGetAmendmentDetails(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid Amendment Details Request", func(t *testing.T) {
		amendmentID := "AMD123456789"

		response, err := service.GetAmendmentDetails(context.Background(), amendmentID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, amendmentID, response.AmendmentID)
		assert.Equal(t, "CONFIRMED", response.Status)
		assert.NotNil(t, response.AmendmentDetails)
	})
}

// TestTripJackGetUserDetails tests the TripJack user details API integration
func TestTripJackGetUserDetails(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Valid User Details Request", func(t *testing.T) {
		userID := "USER123456789"

		response, err := service.GetUserDetail(context.Background(), userID)
		require.NoError(t, err)
		require.NotNil(t, response)

		// Validate response structure
		assert.Equal(t, userID, response.UserID)
		assert.Equal(t, "SUCCESS", response.Status)
		assert.NotNil(t, response.UserDetails)
	})
}

// TestTripJackErrorHandling tests error handling for all TripJack APIs
func TestTripJackErrorHandling(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Search with Invalid Request", func(t *testing.T) {
		// Test with empty request
		request := models.FlightSearchRequest{}

		response, err := service.SearchFlights(context.Background(), request)
		require.NoError(t, err) // Service handles errors gracefully
		require.NotNil(t, response)

		// Should return background task response
		assert.Equal(t, 202, response.Code)
		assert.False(t, response.Completed)
		assert.Equal(t, "background_task", response.DataSource)
	})

	t.Run("Pricing with Invalid FareID", func(t *testing.T) {
		request := models.PricingRequest{
			Trips: []models.PricingTrip{
				{
					Amount:  5000.0,
					Index:   "", // Empty fare ID
					OrderID: 1,
					TUI:     "test-tui",
				},
			},
			ClientID: "",
			Mode:     "SS",
			Options:  "A",
			Source:   "SF",
			TripType: "O",
		}

		// Should error due to empty priceIds
		_, err := service.GetFlightPricing(context.Background(), request)
		assert.Error(t, err)
	})

	t.Run("Booking with Invalid Request", func(t *testing.T) {
		// Test with minimal invalid request
		request := models.BookingRequest{
			FareID: "test-fare-id-123",
			// Missing required fields
		}

		_, err := service.BookFlight(context.Background(), request)
		assert.Error(t, err) // This should error due to empty passengers
	})
}

// TestTripJackRequestValidation tests request validation for all APIs
func TestTripJackRequestValidation(t *testing.T) {
	server := setupTripJackTestServer()
	defer server.Close()

	service := createTestFlightService(server.URL)

	t.Run("Search Request Validation", func(t *testing.T) {
		// Test various invalid search requests
		testCases := []struct {
			name    string
			request models.FlightSearchRequest
		}{
			{
				name: "Missing Cabin",
				request: models.FlightSearchRequest{
					FareType: "REGULAR",
					ADT:      1,
					Trips: []models.TripInfo{
						{From: "DEL", To: "BOM", OnwardDate: "2025-06-12"},
					},
				},
			},
			{
				name: "Invalid Passenger Count",
				request: models.FlightSearchRequest{
					FareType: "REGULAR",
					ADT:      0, // Invalid
					Cabin:    "E",
					Trips: []models.TripInfo{
						{From: "DEL", To: "BOM", OnwardDate: "2025-06-12"},
					},
				},
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				response, err := service.SearchFlights(context.Background(), tc.request)
				require.NoError(t, err, "Service handles errors gracefully for %s", tc.name)
				require.NotNil(t, response)

				// Should return background task response for invalid requests
				if tc.name == "Invalid Passenger Count" {
					assert.Equal(t, 202, response.Code)
					assert.False(t, response.Completed)
					assert.Equal(t, "background_task", response.DataSource)
				} else {
					// Missing cabin might still work with mock server
					assert.NotNil(t, response)
				}
			})
		}
	})

	t.Run("Booking Request Validation", func(t *testing.T) {
		// Test various invalid booking requests
		testCases := []struct {
			name    string
			request models.BookingRequest
		}{
			{
				name: "Missing FareID",
				request: models.BookingRequest{
					TUI: "test-tui",
					// Missing FareID
				},
			},
			{
				name: "Missing Travellers",
				request: models.BookingRequest{
					FareID: "test-fare-id-123",
					TUI:    "test-tui",
					// Missing Travellers
				},
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				_, err := service.BookFlight(context.Background(), tc.request)
				assert.Error(t, err, "Expected error for %s", tc.name)
			})
		}
	})
}
