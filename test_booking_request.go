package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func main() {
	fmt.Println("🧪 Testing Booking Request Structure")
	fmt.Println("============================================================")

	baseURL := "http://localhost:8080"
	
	// Test booking request that matches the frontend structure
	bookingReq := map[string]interface{}{
		"flight_booking": map[string]interface{}{
			"provider_info": map[string]interface{}{
				"code": "1G",
			},
			"TUI":               "flight_search_BOM_DEL_2025-06-02_1_0_0_E",
			"ADT":               1,
			"CHD":               0,
			"INF":               0,
			"NetAmount":         4686.5,
			"AirlineNetFare":    4686.5,
			"SSRAmount":         0,
			"CrossSellAmount":   0,
			"GrossAmount":       4686.5,
			"Hold":              false,
			"ActualHoldTime":    0,
			"ActualDisplayTime": 0,
			"Trips": []map[string]interface{}{
				{
					"Journey": []map[string]interface{}{
						{
							"Provider":  "1G",
							"Stops":     0, // This should now work as int
							"Offer":     "DefaultOffer",
							"OrderID":   1,
							"GrossFare": 4686.5,
							"NetFare":   4686.5,
							"Segments": []map[string]interface{}{
								{
									"Flight": map[string]interface{}{
										"FUID":           "1",
										"VAC":            "SG",
										"MAC":            "SG",
										"OAC":            "SG",
										"Airline":        "SpiceJet",
										"FlightNo":       "8905",
										"ArrivalTime":    "",    // Empty string should be allowed
										"DepartureTime":  "",    // Empty string should be allowed
										"ArrivalCode":    "",    // Empty string should be allowed
										"DepartureCode":  "",    // Empty string should be allowed
										"Duration":       "2h 30m",
										"FareBasisCode":  "",
										"ArrAirportName": "Delhi Indira Gandhi Intl",
										"DepAirportName": "Chhatrapati Shivaji",
										"RBD":            "",
										"Cabin":          "ECONOMY",
										"Refundable":     "N",
									},
									"Fares": map[string]interface{}{
										"GrossFare": 4686.5,
										"NetFare":   4686.5,
									},
								},
							},
						},
					},
				},
			},
		},
		"Travellers": []map[string]interface{}{
			{
				"ID":          1,
				"PaxID":       1,
				"Title":       "Mr",
				"FName":       "ALI",
				"LName":       "K",
				"Age":         25,
				"DOB":         "1989-05-15",
				"Gender":      "M",
				"PTC":         "adult",
				"PLI":         "New York",
				"PDOE":        "2030-12-31",
				"Nationality": "US",
				"PassportNo":  "A123456789",
				"VisaType":    nil,
				"DocType":     "Other",
			},
		},
		"ContactInfo": map[string]interface{}{
			"Title":             "Mr",
			"FName":             "ALI",
			"LName":             "K",
			"Mobile":            "09321116965",
			"Phone":             nil,
			"Email":             "<EMAIL>",
			"Address":           "123 Elm street, New York , NY ,USA",
			"CountryCode":       "+91",
			"MobileCountryCode": "+91",
			"State":             "NY",
			"City":              "New York",
			"PIN":               nil,
			"GSTAddress":        nil,
			"GSTCompanyName":    nil,
			"GSTTIN":            nil,
			"UpdateProfile":     false,
			"IsGuest":           true,
			"SaveGST":           false,
			"Language":          nil,
		},
	}

	jsonData, err := json.Marshal(bookingReq)
	if err != nil {
		fmt.Printf("❌ Failed to marshal request: %v\n", err)
		return
	}

	fmt.Printf("📤 Request: %s\n", string(jsonData))
	fmt.Println("------------------------------------------------------------")

	// Create request with auth header
	req, err := http.NewRequest("POST", baseURL+"/apis/create-booking/", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Failed to create request: %v\n", err)
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************.-harPGDCgFchYzpgxg93zAaLRH3jDuNjYeWZnPnH-u4")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Request failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Failed to read response: %v\n", err)
		return
	}

	fmt.Printf("📥 Response Status: %d\n", resp.StatusCode)
	fmt.Printf("📥 Response Body: %s\n", string(body))

	if resp.StatusCode == 200 || resp.StatusCode == 201 {
		fmt.Println("\n✅ SUCCESS! Booking request structure is now working!")
	} else {
		fmt.Printf("\n❌ Failed with status %d\n", resp.StatusCode)
		
		// Try to parse error response
		var errorResp map[string]interface{}
		if err := json.Unmarshal(body, &errorResp); err == nil {
			if msg, ok := errorResp["message"].(string); ok {
				fmt.Printf("Error details: %s\n", msg)
			}
		}
	}
}
