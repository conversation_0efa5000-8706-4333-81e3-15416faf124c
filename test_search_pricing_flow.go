package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// Test structures matching the API
type FlightSearchRequest struct {
	SecType           string     `json:"SecType"`
	FareType          string     `json:"FareType"`
	ADT               int        `json:"ADT"`
	CHD               int        `json:"CHD"`
	INF               int        `json:"INF"`
	Cabin             string     `json:"Cabin"`
	Source            string     `json:"Source"`
	Mode              string     `json:"Mode"`
	ClientID          string     `json:"ClientID"`
	IsMultipleCarrier bool       `json:"IsMultipleCarrier"`
	IsRefundable      bool       `json:"IsRefundable"`
	TUI               string     `json:"TUI"`
	YTH               int        `json:"YTH"`
	Trips             []TripInfo `json:"Trips"`
}

type TripInfo struct {
	From       string `json:"From"`
	To         string `json:"To"`
	OnwardDate string `json:"OnwardDate"`
	TUI        string `json:"TUI"`
}

type PricingRequest struct {
	Trips    []PricingTrip `json:"Trips"`
	ClientID string        `json:"ClientID"`
	Mode     string        `json:"Mode"`
	Options  string        `json:"Options"`
	Source   string        `json:"Source"`
	TripType string        `json:"TripType"`
}

type PricingTrip struct {
	Amount      float64 `json:"Amount"`
	Index       string  `json:"Index"`
	ChannelCode *string `json:"ChannelCode"`
	OrderID     int     `json:"OrderID"`
	TUI         string  `json:"TUI"`
}

type SearchResponse struct {
	TUI       string         `json:"TUI"`
	Completed bool           `json:"Completed"`
	Trips     []TripResponse `json:"Trips"`
	Code      int            `json:"Code"`
	Msg       []string       `json:"Msg"`
}

type TripResponse struct {
	Journey []Journey `json:"Journey"`
}

type Journey struct {
	Index     string  `json:"Index"`
	Provider  string  `json:"Provider"`
	GrossFare float64 `json:"GrossFare"`
	NetFare   float64 `json:"NetFare"`
}

func main() {
	baseURL := "http://localhost:8080"

	fmt.Println("🚀 Starting Fresh Flight Search and Pricing Test")
	fmt.Println("============================================================")

	// Step 1: Perform a fresh flight search
	fmt.Println("\n📋 Step 1: Performing Fresh Flight Search...")
	searchResult, err := performFlightSearch(baseURL)
	if err != nil {
		fmt.Printf("❌ Search failed: %v\n", err)
		return
	}

	fmt.Printf("✅ Search completed successfully!\n")
	fmt.Printf("   TUI: %s\n", searchResult.TUI)
	fmt.Printf("   Completed: %v\n", searchResult.Completed)
	fmt.Printf("   Found %d trips\n", len(searchResult.Trips))

	if len(searchResult.Trips) == 0 {
		fmt.Println("❌ No trips found in search results")
		return
	}

	// Extract first available flight
	firstTrip := searchResult.Trips[0]
	if len(firstTrip.Journey) == 0 {
		fmt.Println("❌ No journeys found in first trip")
		return
	}

	firstJourney := firstTrip.Journey[0]

	// Extract core flight index (remove |0|TJ suffix if present)
	flightIndex := firstJourney.Index
	if len(flightIndex) > 0 {
		// Split by | and take the first part
		parts := strings.Split(flightIndex, "|")
		if len(parts) > 0 {
			flightIndex = parts[0]
		}
	}

	fmt.Printf("   Original Flight Index: %s\n", firstJourney.Index)
	fmt.Printf("   Core Flight Index: %s\n", flightIndex)
	fmt.Printf("   Provider: %s\n", firstJourney.Provider)
	fmt.Printf("   Gross Fare: %.2f\n", firstJourney.GrossFare)

	// Step 2: Wait a moment for search to complete if needed
	if !searchResult.Completed {
		fmt.Println("\n⏳ Search not completed, waiting 5 seconds...")
		time.Sleep(5 * time.Second)

		// Try to get completed results
		searchResult, err = getSearchResults(baseURL, searchResult.TUI)
		if err != nil {
			fmt.Printf("❌ Failed to get completed search results: %v\n", err)
			return
		}

		if len(searchResult.Trips) == 0 || len(searchResult.Trips[0].Journey) == 0 {
			fmt.Println("❌ Still no valid flights available")
			return
		}

		firstJourney = searchResult.Trips[0].Journey[0]
		fmt.Printf("✅ Updated Flight Index: %s\n", firstJourney.Index)
	}

	// Step 3: Perform pricing request with fresh data
	fmt.Println("\n💰 Step 2: Performing Pricing Request...")
	pricingResult, err := performPricingRequest(baseURL, firstJourney, searchResult.TUI)
	if err != nil {
		fmt.Printf("❌ Pricing failed: %v\n", err)
		return
	}

	fmt.Printf("✅ Pricing completed successfully!\n")
	fmt.Printf("   Response: %s\n", pricingResult)

	fmt.Println("\n🎉 Test completed successfully!")
}

func performFlightSearch(baseURL string) (*SearchResponse, error) {
	// Create search request for tomorrow's date
	tomorrow := time.Now().AddDate(0, 0, 1).Format("2006-01-02")

	searchReq := FlightSearchRequest{
		SecType:           "D",
		FareType:          "REGULAR",
		ADT:               1,
		CHD:               0,
		INF:               0,
		Cabin:             "E",
		Source:            "CF",
		Mode:              "AS",
		ClientID:          "",
		IsMultipleCarrier: false,
		IsRefundable:      false,
		TUI:               "",
		YTH:               0,
		Trips: []TripInfo{
			{
				From:       "DEL",
				To:         "BOM",
				OnwardDate: tomorrow,
				TUI:        "",
			},
		},
	}

	return makeAPIRequest[SearchResponse](baseURL+"/apis/search", searchReq)
}

func getSearchResults(baseURL, tui string) (*SearchResponse, error) {
	searchListReq := map[string]string{
		"TUI": tui,
	}

	return makeAPIRequest[SearchResponse](baseURL+"/apis/search_list", searchListReq)
}

func performPricingRequest(baseURL string, journey Journey, tui string) (string, error) {
	pricingReq := PricingRequest{
		Trips: []PricingTrip{
			{
				Amount:      journey.GrossFare,
				Index:       journey.Index,
				ChannelCode: nil,
				OrderID:     1,
				TUI:         tui,
			},
		},
		ClientID: "",
		Mode:     "SS",
		Options:  "A",
		Source:   "SF",
		TripType: "O",
	}

	return makeAPIRequestRaw(baseURL+"/apis/pricing/", pricingReq)
}

func makeAPIRequest[T any](url string, payload any) (*T, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var result T
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &result, nil
}

func makeAPIRequestRaw(url string, payload any) (string, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	fmt.Printf("   Request URL: %s\n", url)
	fmt.Printf("   Request Payload: %s\n", string(jsonData))

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	fmt.Printf("   Response Status: %d\n", resp.StatusCode)
	fmt.Printf("   Response Body: %s\n", string(body))

	if resp.StatusCode != 200 {
		return string(body), fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	return string(body), nil
}
